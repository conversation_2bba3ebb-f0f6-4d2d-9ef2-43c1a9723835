import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { CustomFrame } from '@/Pages/CustomFrame.tsx';
import FromToSelector from '@/Common/Components/datePicker/FromToSelector';
import Input from '@/Common/Components/common/Input';
import DropDown from '@/Common/Components/common/DropDown';
import { Button } from '@/Common/Components/common/Button';
import CommonTable from '@/Common/Components/common/CommonTable';
import completed from '@/assets/images/status/completed.svg';
import due from '@/assets/images/status/due.svg';
import overdue from '@/assets/images/status/overdue.svg';

const FleetFault = () => {
  const { t } = useTranslation();

  const navigate = useNavigate();

  const statusIcon: Record<string, string> = {
    completed,
    due,
    overdue,
  };

  const columns = [
    { header: t('FaultOccurrenceTime'), accessorKey: 'time' },
    { header: t('ModelName'), accessorKey: 'model' },
    { header: t('VehicleNumber'), accessorKey: 'vehicle' },
    { header: t('Mileage'), accessorKey: 'mileage' },
    {
      header: t('FaultSeverity'),
      accessorKey: 'severity',
      cell: ({ getValue }: { getValue: () => unknown }) => {
        const value = String(getValue() ?? '');
        const isHigh = value.toLowerCase() === 'high';
        return <span className={isHigh ? 'text-semantic-4' : ''}>{value}</span>;
      },
    },
    { header: t('DriverName'), accessorKey: 'driver' },
    {
      header: t('Status'),
      accessorKey: 'status',
      cell: ({ getValue }: { getValue: () => unknown }) => {
        const key = String(getValue() ?? '').toLowerCase();
        const src = statusIcon[key];
        return <img src={src} alt={key} title={key} />;
      },
    },
  ];

  const data = [
    {
      time: '2025-04-08 14:32',
      model: '25B-X',
      vehicle: '25B-1234124',
      mileage: '17,213mi',
      severity: 'High',
      driver: 'Sophia Elizabeth Davis',
      status: 'completed',
    },
  ];

  return (
    <CustomFrame name={t('FaultManagement')} back={false}>
      <section className="wrap-layout">
        {/* 검색 필터 */}
        <article className="mb-[18px] f-c gap-4">
          <div className="f-c gap-[10px]">
            <FromToSelector />
            <Input widthSize="md" placeholder={t('VehicleNumber')} />
            <Input widthSize="md" placeholder={t('DriverName')} />
            <DropDown size="md" placeholder={t('AllSeverity')} options={[]} />
            <DropDown size="md" placeholder={t('AllStatus')} options={[]} />
          </div>
          <Button variant={'bt_primary'} label={t('Search')} />
        </article>

        {/* 다운로드 버튼 및 테이블 */}
        <article>
          <div className="mb-[10px] f-je">
            <Button variant={'bt_tertiary_sm'} label={t('Download')} />
          </div>

          <CommonTable
            columns={columns}
            data={data}
            isPagination={true}
            onRowClick={() => navigate(`/fleet-fault-detail`)}
            tdclassName="cursor-pointer"
          />
        </article>
      </section>
    </CustomFrame>
  );
};

export default FleetFault;
