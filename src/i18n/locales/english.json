﻿{
  "translation": {
    "Login": "Login",
    "FleetManagement": "Fleet Management",
    "FleetInfo": "Fleet Info",
    "MachineInfo": "Machine Info",
    "WorkOrder": "Work Order",
    "Fleet": "Fleet",
    "Search": "Search",
    "Delete": "Delete",
    "Add": "Add",
    "FleetName": "Fleet Name",
    "FleetNameN": "Fleet",
    "AddFleet": "Add Fleet",
    "Machine": "Machine",
    "Driver": "Driver",
    "DriverCertified": "Driver",
    "MachineList": "Machine List",
    "ChangingSetting": "Changing Setting",
    "SettingHistory": "Setting History",
    "Model": "Model",
    "MachineID": "Machine ID",
    "MachineIDN": "Machine ID",
    "SerialNo": "Serial No.",
    "RefreshU": "Refresh",
    "Country": "Country",
    "Location": "Location",
    "Select": "Select",
    "Total": "Total",
    "Units": "",
    "ID": "ID",
    "IDType": "ID Type",
    "LicenseStartDate": "License Start Date",
    "LicenseExpiryDate": "License Expiry\nDate",
    "StartTime": "Start Time",
    "EndTime": "End Time",
    "OperableEquipment": "Operable Equipment",
    "Close": "Close",
    "AddMachine": "Add Machine",
    "NoSearchResultsFound": "No search results found.",
    "PleaseSelectTheEquipmentToConfigure": "Please select the equipment to configure.",
    "SelectEquipment": "Select Equipment",
    "RegistrationEquipment": "Registration Equipment",
    "ClusterPassword": "Cluster Password",
    "OPSS": "OPSS",
    "HAC": "HAC",
    "AutoShift": "Auto Shift",
    "ZeroStart": "Zero Start",
    "DCSR": "DCSR",
    "SpeedLimit": "Speed Limit",
    "ShiftUp": "Shift Up",
    "ShiftDown": "Shift Down",
    "Kmh": "Km/h",
    "All": "All",
    "Send": "Send",
    "Forwarding": "Forwarding",
    "MessageSend": "Send",
    "TheConfigurationHasBeenUpdated": "The configuration has been updated.",
    "TheConfiguredInformationHasBeenSentToTheRMCU": "The configured information has been sent to the RMCU.",
    "ControlStatus": "Control Status",
    "LastUpdateE": "Last Update",
    "TransmissionDateTime2": "Transmission Date&Time",
    "DateTime": "Transmission Date&Time",
    "ViewHistoryE": "View History",
    "No": "No.",
    "Customer": "Customer",
    "View": "View",
    "SpecifyFleet": "Specify Fleet",
    "MachineInformation": "Machine Information",
    "RMCU": "RMCU",
    "Item": "Item",
    "OperatingHour": "Operating Hour",
    "DeliveryDate": "Delivery Date",
    "WarrantyExpiryDate": "Warranty Expiry Date",
    "DealerName": "Dealer Name",
    "Contact": "Contact",
    "ContactE": "Contact",
    "ServiceContact": "Contact",
    "FaultAlarm": "Fault Alarm",
    "ConsumableAlarm": "Consumable Alarm",
    "LocationDeviationStatus": "Location Deviation Status",
    "CommunicationStatus": "Communication Status",
    "AddDriver": "Add Driver",
    "EditDriver": "Edit Driver",
    "IDNumber": "ID Number",
    "List": "List",
    "PastHistory": "History",
    "History": "History",
    "RegisterWorkOrder": "Register Work Order",
    "Instructor": "Instructor",
    "HimateAdmin": "Himate-admin",
    "Receiver": "Receiver",
    "TargetEquipment": "Target Equipment",
    "WorkDescription": "Description",
    "Mileage": "Mileage",
    "Status": "Status",
    "WorkEndTime": "Work End Time",
    "IdlingH": "Idling",
    "Register": "Register",
    "TheWorkOrderDetailsHaveBeenSent": "The work order details have been sent. ",
    "Date": "Date",
    "Date2": "Date",
    "Completed": "Completed",
    "ShiftStartTime": "Shift Start Time",
    "ShiftEndTime": "Shift End Time",
    "Password": "Password",
    "DriverPassword": "Driver Password",
    "Edit": "Edit",
    "NotTransmitted": "Not Transmitted",
    "DriverRegistration": "Driver Registration",
    "FileUpload": "File Upload",
    "DownloadForms": "Download Forms",
    "CAllInDate": "CAll In Date",
    "Activation": "Activation",
    "Activation2": "Activation",
    "ShiftTimeActivation": "Shift Time Activation",
    "ShiftTime": "Shift Time",
    "SaveE": "Save",
    "ANewPieceOfEquipmentHasBeenRegistered": "A new piece of equipment has been registered.",
    "DriverManagement": "Driver Management",
    "ManageDriver": "Manage Driver",
    "AllItineraryHistory": "All Itinerary History",
    "ManageDriverList": "List",
    "ManageDriverReport": "Report",
    "ViewByEquipment": "View By Equipment",
    "ViewByDriver": "View By Driver",
    "DriverInformation": "Driver Information",
    "Print": "Print",
    "CollisionManagement": "Collision Management",
    "ManageCollision2": "Manage Collision",
    "ManageCollisionList": "List ",
    "ManageCollisionReport": "Report ",
    "RefCollision": "Ref. Collision",
    "RefAccident": "Ref. Accident",
    "RefOverSpeed": "Ref. Over Speed",
    "RefFrontRearCollision": "Ref. Front/Rear",
    "RefSideCollision": "Ref. Side",
    "RefVerticalCollision": "Ref. Vertical",
    "RefFrontRearAccident": "Ref. Front/Rear",
    "RefSideAccident": "Ref. Side",
    "RefVerticalAccident": "Ref. Vertical",
    "TheRecentCollisionCriteriaHaveBeenUpdatedPleaseTransmitTheData": "The recent collision criteria have been updated. Please transmit the data.",
    "OnlyNumbersBetween01And10CanBeEntered": "* Only numbers Between 0.1 and 10 can be entered",
    "OnlyNumbersBetween1And30CanBeEntered": "* Only numbers between 1 and 30 can be entered",
    "Range110": "* Range : 1 ~ 10",
    "Range01250": "* Range : 0.1 ~ 25.0",
    "Range130": "* Range : 1 ~ 30",
    "TheEnteredValueIsLowerThanTheExistingOnePleaseEnterAValidNumber": "The entered value is lower than the existing one. Please enter a valid number.",
    "TheUpdatedInformationHasBeenSaved": "The updated information has been saved.",
    "CollisionInformation": "Collision Information",
    "AccidentInformation": "Accident Information",
    "FrontRearCollision": "Front/Rear",
    "SideCollision": "Side",
    "VerticalCollision": "Vertical",
    "FrontRearAccident": "Front/Rear",
    "SideAccident": "Side",
    "VerticalAccident": "Vertical",
    "PreCheckManagement": "Pre-Check Management",
    "PreCheckCount": "Pre-Check Count",
    "PreCheck": "Pre-Check",
    "PreCheckInformationDetails": "Pre-Check Information Details",
    "PreCheckRegistration": "Pre-Check Registration",
    "PreCheckInformation": "Pre-Check Information",
    "Question": "Question",
    "Answer": "Answer",
    "Critical": "Critical",
    "UnitLock": "Unit Lock",
    "LastUpdate": "Last Update",
    "MachineCount": "Machine Count",
    "DragAndDropAFileOrClick": "Drag and drop a file Or click",
    "ANewPreCheckHasBeenRegistered": "A new pre-check has been registered.",
    "MachineManagement": "Machine Management",
    "HiMATEService": "Hi-MATE Service",
    "RentMachine": "Rent Machine",
    "UsedMachine": "Used Machine",
    "MCUReplace": "MCU Replace",
    "DescendingOrder": "Descending Order",
    "AscendingOrder": "Ascending Order",
    "ServiceStartDate": "Service Start Date",
    "ServiceEndDate": "Service End Date",
    "OK": "OK",
    "Check": "Check",
    "People": "",
    "MachineType": "Machine Type",
    "Normal": "Normal",
    "Test": "Test",
    "MachinNo": "Machin No.",
    "CheckMachine": "Check Machine",
    "ChassisModel": "Chassis Model",
    "RMCUCheck": "RMCU Check",
    "Rent": "Rent",
    "DealershipCode": "Dealership Code",
    "IgnoreServiceStartConditions": "Ignore Service Start Conditions",
    "EMail": "E-Mail",
    "EMailAddress": "E=Mail",
    "Communication": "",
    "CardNo": "Card No.",
    "PINNo": "PIN No.",
    "PIN": "PIN",
    "Extend": "Extend",
    "StartDate": "Start Date",
    "EndDate": "End Date",
    "ExtendStartDate": "Extend Start Date",
    "ExtendEndDate": "Extend End Date",
    "CustomerDeliveryDate": "Customer Delivery Date",
    "EngineMarker": "Engine arker",
    "DesignModel": "Design Model",
    "SalesModel": "Sales Model",
    "Save2": "Save",
    "InvalidValue": "Invalid value.",
    "InvalidVINPleaseTryAgain": "Invalid VIN. Please try again.",
    "FaultManagement": "Fault Management",
    "CurrentFault": "Current Fault",
    "PastFault": "Past Fault",
    "Region": "Region",
    "Severity": "Severity",
    "AlarmType": "Alarm Type",
    "AlarmTypeEng": "Alarm Type",
    "SPN": "SPN",
    "FMI": "FMI",
    "AdvancedFilter": "Advanced Filter",
    "Guide": "Guide",
    "ReceiptStatus": "Receipt Status",
    "NotReceived": "Not Received",
    "DateCriteria": "Date Criteria",
    "FaultOccurrenceDate": "Fault Occurrence Date",
    "FaultResolutionDate": "Fault Resolution Date",
    "FaultResolutionDate2": "Fault Resolution Date",
    "Fault": "Fault",
    "MapV": "Map",
    "ServiceHistory": "Service History",
    "OperatingTime": "Operating Time",
    "ClaimInformation": "Claim Info",
    "ClaimNumber": "Claim Number",
    "ClaimType": "Claim Type",
    "InspectionDetails": "Inspection Details",
    "MaintenanceManagement": "Maintenance Management",
    "State": "State",
    "DateFaultOccurenceDate": "Date Fault Occurence Date",
    "MaintenanceIntervalH": "Maintenance Interval(h)",
    "RemainingTimeToNextMaintenanceH": "Remaining Time To Next Maintenance(h)",
    "PlaceOrder": "Place Order",
    "Order": "Order",
    "Overdue": "Overdue",
    "Approaching": "Approaching",
    "Replacement": "Replacement",
    "FutureSupportPlanned": "Future Support Planned",
    "OutputReport": "Output Report",
    "MachineReport": "Machine Report",
    "FleetReport": "Fleet Report",
    "TotalMachine": "Total Machine",
    "ActiveMachine": "Active Machine",
    "WorkingDays": "Working Days",
    "TotalDrivers": "Total Drivers",
    "OperationHoursHEngineRunWorkingTravelingIdling": "OperationHours(h) Engine Run / Working / Traveling / Idling",
    "ProductivityEfficiencyAnalysis": "Productivity & Efficiency Analysis",
    "MachineUtilizationRate": "Machine Utilization Rate",
    "AverageWorkingHours": "Average Working Hours",
    "OperatinHours": "Operatin Hours",
    "WorkingTraveling": "Working Traveling",
    "WorkingO": "Working",
    "NonOperation": "Non Operation",
    "Repair": "Repair",
    "WorkStartTime": "Work Start Time",
    "QuittingTime": "Quitting Time",
    "TotalWorkingTraveling": "Total Working + Traveling",
    "TotalIdling": "Total Idling",
    "WorkingRankingByModel": "Working Ranking By Model",
    "WorkingRankingByMachine": "Working Ranking By Machine",
    "PDFDownload": "PDF Download",
    "Description": "Description",
    "DescriptionQuestion": "Description",
    "BulkAssign": "Bulk Assign",
    "OperationLimitaion": "Operation Limitation",
    "ConfirmSet": "Confirm",
    "Hong": "Hong",
    "ControlName": "Control Name",
    "TransmissionStatus": "Status",
    "Assign": "Assign",
    "AssignS": "Assign",
    "Dealership": "Dealership",
    "Download": "Download",
    "Type": "Type",
    "Pieces": "",
    "AccountManagement": "Account Management",
    "Permission": "Permission",
    "User": "User",
    "UserID": "User ID",
    "PasswordReset": "Password Reset",
    "AddNew": "Add",
    "Add2": "Add",
    "DealerCode": "Dealer Code",
    "DealerCode2": "DealerCode",
    "Machines": "Machines",
    "RegistrationDate": "Registration Date",
    "ServiceStatus": "Service Status",
    "ActionTakenBy": "Action Taken By",
    "RegisterServiceHistory": "Register Service History",
    "MachineManagementStatusbyDealer": "Machine Management Status by Dealer",
    "MachineManagementStatusbyDealerS": "Machine Management Status by Dealer",
    "NumberOfMachinesCustomerRegistrationRentalUsedAll": "Number of Machines Customer Registration / Rental / Used / All",
    "NumberOfRegisteredCustomers": "Number of Registered Customers",
    "DealerInformation": "Dealer Information",
    "TotalMachines": "Total Machines",
    "CustomerRegistration": "Customer Registration",
    "Used": "Used",
    "Rental": "Rental",
    "MachineList2": "Machine List",
    "UserList": "User List",
    "UserId": "User ID",
    "UserName": "User Name",
    "CumminsEngineInformation": "Cummins Engine Information",
    "Summary": "Summary",
    "RemoteDiagnosis": "Remote Diagnosis",
    "ECDTargetMachine": "ECD Target Machine",
    "ECDOnMachine": "ECD On Machine",
    "ECDOffMachine": "ECD off Machine",
    "EngineStatus": "Engine Status",
    "MachineWithECDSettingBeingChanged": "Machine With ECD Setting Being Changed",
    "CumulativeNumberOfReceivedMessagesPerMonth": "Cumulative Number of Received Messages per Month",
    "CumulativeNumberOfReportsPerMonth": "Cumulative Number of Reports per Month",
    "MonthlyCumulativeOccurrenceFrequencyMaximumFaultCodeTop10": "Monthly Cumulative Occurrence Frequency Maximum Fault Code Top 10",
    "ECDONOFF": "ECD ON/OFF",
    "MailReceptionList": "Mail Reception List",
    "RecentRequestDateandTime": "Recent Request Date and Time",
    "VehicleDistance": "Vehicle Distance",
    "CalibrationIdentification": "Calibration Identification",
    "CalibrationVerificationNumber": "Calibration Verification Number",
    "NumberOfSoftwareIdentification": "Number of Software Identification",
    "EngineSerialNumberEng": "Engine Serial Number",
    "VIN": "VIN",
    "Make": "Make",
    "ModelEng": "Model",
    "ECDStatus": "ECD Status",
    "UnitNumberEng": "Unit Number",
    "ECDDetails": "ECD Details",
    "ECDInformation": "ECD Information",
    "ECDActive": "ECD Active",
    "Request": "Request",
    "RecentResponseDate": "Recent Response Date(Korea)",
    "FCLimitationMessageDay": "F/C Limitation(message/day)",
    "HBLimitationMessageDay": "H/B Limitation(message/day)",
    "NoDataAvailable": "No Data Available.",
    "RevDate": "Rev Date",
    "CommType": "Comm Type",
    "NumberOfOccurrences": "Number of Occurrences",
    "MsgType": "Msg Type",
    "ECDRequest": "ECD Request",
    "FaultListSendPayloadList": "Fault List & Send Payload List",
    "ReceivedMessage": "Received Message",
    "CumminsRecipentList": "Cummins Recipent List",
    "DateLocal": "Date (Local)",
    "CMISendResult": "CMI Send Result",
    "SendActive": "Send Active",
    "SendType": "Send Type",
    "NOTIFICATIONID": "NOTIFICATION_ID",
    "CustomerEquipmentID": "Customer Equipment ID",
    "EngineSerialNumber": "Engine Serial Number",
    "VINOEMEquipmentID": "VIN / OEM Equipment ID",
    "EngineModel": "Engine Model",
    "RecommendedAction": "Recommended Action",
    "FaultCodeFCInformation": "Fault Code(FC) Information",
    "FaultCode": "Fault Code",
    "Probability": "Probability",
    "TimeStamp": "Time Stamp",
    "ExpectedCause": "Expected Cause",
    "StartDate2": "Start Date",
    "EndDate2": "End Date",
    "RemoteDiagnosisAnnouncement": "To identify the equipment location when the fault occurred and nearby Cummins certified service providers use Cummins Service Locator. At the time of service, a Cummins certified service technician will perform standard diagnostics to determine a more detailed root cause and the required repairs. If you require further assistance, please contact Cummins Care at 1-800-CUMMINS (**************) and we will be happy to assist you.",
    "ElapsedTime": "Elapsed Time",
    "EngineOilPressureKPa": "Engine Oil Pressure(kPa)",
    "TransmissionOilTemperature": "Transmission Oil Temperature(℃)",
    "EngineFuelRateLh": "Engine Fuel Rate(L/h)",
    "BarometricPressureKPa": "Barometric Pressure(kPa)",
    "EngineSpeedRpm": "Engine Speed(rpm)",
    "EngineLoadatCurrentSpeed": "Engine Load at Current Speed(%)",
    "AirIntakePressureKPa": "Air Intake Pressure(kPa)",
    "BatteryVoltageV": "Battery Voltage(V)",
    "ActualTorque": "Actual Torque(%)",
    "DEFTank": "DEF Tank(%)",
    "Operationcondition": "Operation condition",
    "FuelLevel": "Fuel Level(%)",
    "IntakeManifold1Term": "Intake Manifold 1 Term(℃)",
    "CoolantTemperature": "Coolant Temperature(℃)",
    "FuelTemperature": "Fuel Temperature(℃)",
    "HydraulicOilTemperature": "Hydraulic Oil Temperature(°C)",
    "FaultCodeEng": "Fault Code",
    "DealerDetails": "Dealer Details",
    "Recipient": "Recipient",
    "EmailEng": "E-mail",
    "ECDMailList": "ECD Mail List",
    "DealerRecipientList": "Dealer Recipient List",
    "Report": "Report",
    "ReportEng": "Report",
    "ConfirmPop": "확인",
    "FAQ": "FAQ",
    "Account": "Account",
    "SearchTC": "Search",
    "SearchByTitleContent": "Search by title/content",
    "ContentFAQ": "Content",
    "Title": "Title",
    "DateRes": "Date",
    "FAQAdd": "Add",
    "ReadPermission": "Read Permission",
    "Language": "Language",
    "PinToTop": "Pin To Top",
    "TemporaryStorage2": "Temporary Storage 2",
    "QType": "Type",
    "AreYouSureYouWantToDeleteTheFAQ": "Are you sure you want to delete the FAQ?",
    "Cancel": "Cancel",
    "Save": "Save",
    "FAQDeleted": "FAQ Deleted.",
    "SaveTemporarily": "Save Temporarily.",
    "NoFAQRegistered": "No FAQ registered.",
    "NoResultsFound": "No results found.",
    "QNA": "Q&A",
    "RegisterW": "Register",
    "QNARes": "Q&A",
    "TypeInquiry": "Type",
    "Content": "Content",
    "AttatchFile": "Attatch File",
    "DropFilesHereOrClickToUpload": "Drop files here or click to upload",
    "UpTo5FilesCanBeUploadedJpgJpegBmpPngGif": "Up to 5 files can be uploaded:jpg, jpeg, bmp, png, gif",
    "UpTo5FilesCanBeUploadedJpgJpegBmpPngGifPdf": "Up to 5 files can be uploaded:jpg, jpeg, bmp, png, gifm pdf",
    "TitleThereAreUnsavedChangesIfYouLeaveWithoutSavingAnyUnsavedContentWillBeLost": "There are unsaved changes.\nIf you leave without saving, any unsaved content will be lost.",
    "Savedtemporarily": "Saved temporarily.",
    "ReplyI": "Reply",
    "NoQNAregistered": "No Q&A registered.",
    "MyInfo": "My Info",
    "NotificationSettings": "Notification Settings",
    "OptionalPersonalInformationCollectionAndUsage": "Optional Personal Information Collection And Usage",
    "MobileNumber": "Mobile Number",
    "TimeSetting": "Time Setting",
    "City": "City",
    "GMT": "GMT",
    "DateFormatUnitSettings": "Date Format Unit Settings",
    "Distance": "Distance",
    "Volume": "Volume",
    "Temperature": "Temperature",
    "Pressure": "Pressure",
    "Weight": "Weight",
    "DomainSelection": "Domain Selection",
    "Km": "Km",
    "Mile": "mile",
    "L": "L",
    "Gal": "gal",
    "C": "˚C",
    "F": "˚F",
    "kgfCm": "kgf/cm²",
    "psi": "psi",
    "ton": "ton",
    "lb": "lb",
    "USTon": "US ton",
    "ChangePassword": "Change Password",
    "CurrentPassword": "Current Password",
    "NewPassword": "New Password",
    "ConfirmNewPassword": "Confirm new password",
    "EnterBetween10To16Characters": "Enter between 10 To 16 characters.",
    "MixTypesLettersNumbersAndSpecialCharacters": "*  Mix types: letters, numbers, and special characters(!@#$%^*()-_=+~)",
    "NoConsecutive3CharactersOrNumbersInARowOnTheKeyboard": "*  No consecutive 3 characters or numbers in a row on the keyboard.",
    "ChangeE": "Change",
    "PleaseEnterYourPassword": "Please enter your password.",
    "PasswordsMatch": "Password match.",
    "PasswordsDoNotMatch": "Passwords do not match.",
    "CollisionAlarm": "Collision Alarm",
    "OtherAlarm": "Other Alarm",
    "ReceiveAlertsOnWeb": "Receive Alerts on Web",
    "ReceiveAlertsOnApp": "Receive Alerts on App",
    "Fleet2": "Fleet",
    "Alarm": "Alarm",
    "EditC": "Edit",
    "SaveC": "Save",
    "JustTheNumbers": "Just the numbers.",
    "DDMMYY": "MM/DD/YY",
    "ContentQA": "Content",
    "ContentReply": "Content",
    "Pending": "Pending",
    "CompletedQA": "Completed",
    "Deleted": "Deleted",
    "Notice": "Notice",
    "NoticeAdd": "Notice Add",
    "AddNotice": "Add",
    "TypeNotice": "Type",
    "RegisterNotice": "Register",
    "ViewNum": "View",
    "Admin": "Admin",
    "TitleNotice": "Title Notice.",
    "Previous": "Previous",
    "NextT": "Next",
    "NoticePeriod": "Notice Period",
    "Always": "Always",
    "Period": "Period",
    "ViewRead": "View",
    "NoNoticeRegistered": "No Notice registered.",
    "TemporaryStorage": "Temporary Storage",
    "ThereAreUnsavedChanges": "There are unsaved changes.",
    "IfYouLeaveWithoutSaving": "If you leave without saving,",
    "AnyUnsavedContentWillBeLost": "any unsaved content will be lost.",
    "ManualDownload": "Manual Download",
    "DriverManual": "Driver Manual",
    "ServiceManual": "Service Manual",
    "TheManualProvidedForTheOperatorIncludes": "The manual Provided for the operator includes",
    "UsageInstructionsSafetyWarningsAndMaintenanceInformation": "usage instructions, safety/warnings, and maintenance information.",
    "TheManualProvidedForTheOperatorIncludes2": "The manual Provided for the operator includes",
    "UsageInstructionsSafetywarningsAndMaintenanceInformation2": "usage instructions, safety/warnings, and maintenance information.",
    "WarningNotice": "Warning Notice",
    "ThisManualIsIntendedForTheSoleAndExclusiveUseOfYourselfAndContainsInformationThatIscCnfidential": "This manual is intended for the sole and exclusive use of yourself and contains information that is confidential.",
    "YouQAreHerebyNotifiedThatAnyUseiDsseminationDistributionOrcopyingOfTheManualOrTheInformationContainedThereinIsSTRICTLYPROHIBITED": "You are hereby notified that any use, dissemination, distribution or copying of the manual or the information contained therein is STRICTLY PROHIBITED.",
    "Approver": "Approver",
    "DateW": "Date",
    "StatusD": "Status",
    "TSG": "TSG",
    "TSGTitle": "TSG Title",
    "Code": "Code",
    "Reason": "Reason",
    "Result": "Result",
    "StandardInformation": "Standard Information",
    "Circuit": "Circuit",
    "ComponentLocation": "Com-ponent Location",
    "StatusEng": "Status",
    "AddtionInformation": "Addtion Information",
    "TroubleShootingProcedure": "Trouble Shooting Procedure",
    "AdvancePreparation": "Advance Preparation",
    "Step1": "Step 1",
    "CheckingWay": "Checking Way",
    "CheckW": "Check",
    "Judgement": "Judgement",
    "YesNextProcedure": "Yes (Next Procedure)",
    "NoNextProcedure": "No (Next Procedure)",
    "TSGCreate": "TSG Create",
    "TSGManage": "TSG Manage",
    "TSGBoard": "TSG Board",
    "ImageBox": "Image Box",
    "ModelGroup": "Model Group",
    "HCESPN": "(HCE)SPN",
    "HCESPNN": "HCESPN",
    "Remark": "Remark",
    "RemarkEng": "Remark",
    "RequestA": "Request",
    "Level": "Level",
    "Dashboard": "Dashboard",
    "MachineDetails": "Machine Details",
    "Monitoring": "Monitoring",
    "Statistics": "Statistics",
    "Management": "Management",
    "MMC": "MMC",
    "MachineCommunication": "Machine Communication",
    "SubscriptionManagement": "Subscription Management",
    "Calendar": "Calendar",
    "ServiceInformation": "Service Information",
    "FaultMaintenanceAnalysis": "Fault & Maintenance Analysis",
    "FaultAnalysis": "Fault Analysis",
    "TotalAlarm": "Total",
    "Cases": "",
    "PendingAlarm": "Pending",
    "ResponseAlarm": "Response",
    "CompletedAlarm": "Completed",
    "MaintenanceAnalysis": "Maintenance Analysis",
    "TotalR": "Total",
    "OverdueE": "Overdue",
    "Due": "Due",
    "CompletedE": "Completed",
    "FaultAlarmRankingByModel": "Fault Alarm Ranking by Model",
    "MaintenanceDueItemTrends": "Maintenance Due Item Trends",
    "Daily": "Daily",
    "Weekly": "Weekly",
    "Monthly": "Monthly",
    "Operator": "Operator",
    "RankingByFaultSeverity": "Ranking by Fault Severity",
    "MaintenanceOverdueItemTrends": "Maintenance Overdue Item Trends",
    "FaultResponseDelayRate": "Fault Response Delay Rate",
    "MaintenanceReplacementDelayRate": "Maintenance Replacement Delay Rate",
    "ViewDetail": "View Detail",
    "OperatingHours": "Operating Hours",
    "MonthlyWorkHours": "Monthly Work Hours",
    "Idling": "Idling",
    "TotalIdlingT": "Total Idling",
    "WorkingRankingByOperator": "Working Ranking by Operator",
    "IdleTimeRankingByOperator": "Idle Time Ranking by Operator",
    "EquipmentOperationAnalysisByOperator": "Equipment Operation Analysis by Operator",
    "TotalOperatingHours": "Total Operating Hours",
    "WorkingH": "Working",
    "Traveling": "Traveling",
    "WorkingTransitionAnalysis": "Working Transition Analysis",
    "CumulativeTime": "Cumulative Time",
    "DealerD": "Dealer",
    "Scope": "Scope",
    "DateD": "Date",
    "MonthM": "Month",
    "Year": "Year",
    "Refresh": "Refresh",
    "YearAverage": "Year Average",
    "Average": "Average",
    "Jan": "Jan",
    "Feb": "Feb",
    "Mar": "Mar",
    "Apr": "Apr",
    "May": "May",
    "Jun": "Jun",
    "Jul": "Jul",
    "Aug": "Aug",
    "Sep": "Sep",
    "Oct": "Oct",
    "Nov": "Nov",
    "Dec": "Dec",
    "Africa": "Africa",
    "China": "China",
    "India": "India",
    "Korea": "Korea",
    "LatinAmerica": "Latin America",
    "MiddleEast": "Middle East",
    "NorthAmerica": "North America",
    "Oceania": "Oceania",
    "CIS": "CIS",
    "Brazil": "Brazil",
    "Turkiye": "Turkiye",
    "Asia": "Asia",
    "Europe": "Europe",
    "WorkingHourAnalysis": "Working Hour Analysis",
    "Hours": "Hours",
    "Minutes": "Min",
    "EngineRunningTime": "Engine Running Time",
    "EngineRunningTimeAvg": "Engine Running Time(Avg.)",
    "RegionCountry": "Region/Country",
    "WorkingDaysAvg": "Working Days(Avg.)",
    "PeriodD": "Period",
    "Periodical": "Periodical",
    "Days": "Days",
    "FuelEfficiency": "Fuel Efficiency",
    "FuelConsumption": "Fuel Consumption",
    "FuelConsumption2": "Fuel Consumption",
    "FuelConsumptionAnalysis": "Fuel Consumption Analysis",
    "Month": "Month",
    "EGRunHourH": "E/G Run Hour(h)",
    "WorkingHourH": "Working Hour(h)",
    "TravelingHourH": "Traveling Hour(h)",
    "IdlingHourH": "Idling Hour(h)",
    "FuelRateLH": "Fuel Rate(L/h)",
    "FuelUsedL": "Fuel Used(L)",
    "EstimatedFuelConsumption": "Estimated Fuel Consumption",
    "EngineRunTT": "Engine Run",
    "ActualWorkingT": "Actual Working",
    "TravelingT": "Traveling",
    "FuelLevelT": "Fuel Level",
    "CAUTIONTheFuelUsedDataReflectedAboveIsTheoreticalAndMayDifferFromTheActualUsedBasedOnTheEnvironmentOrHowTheMachineWasOperated": "* CAUTION :The fuel used data reflected above is theoretical and may differ from the actual used; based on the environment or how the machine was operated.",
    "BatteryAnalysis": "Battery Analysis",
    "ChargingCountByModel": "Charging count by Model",
    "BatteryConsumption": "Battery Consumption",
    "BatteryConsumptionAnalysis": "Battery Consumption Analysis",
    "LithiumBattery": "Lithium Battery",
    "LithiumBatteryInformation": "Lithium Battery Information",
    "EngineRunB": "Engine Run",
    "ActualWorkingB": "Actual Working",
    "TravelingB": "Traveling",
    "BatteryUsedB": "Battery Used",
    "TotalChangingCount": "Total Changing Count",
    "AverageSOH": "Average SOH",
    "ChargingCountByEquipment": "Charging Count by Equipment",
    "SOHDegradation": "SOH Degradation",
    "PackCurrent": "Pack Current",
    "PackVoltage": "Pack Voltage",
    "CollingWaterTemperature": "Colling Water Temperature",
    "BTMSToBattery": "BTMS to Battery",
    "BTMSConsumerPower": "BTMS Consumer Power",
    "BatteryHighestTemperature": "Battery Highest Temperature",
    "CoolingFAN": "Cooling FAN",
    "AccumulatedChargingCapacity": "Accumulated Charging Capacity",
    "AccumulatedDischargingCapacity": "Accumulated Discharging Capacity",
    "MaxCellVoltage": "Max Cell Voltage",
    "MinCellVoltage": "Min Cell Voltage",
    "MaxModuleTemperature": "Max Module Temperature",
    "MinModuleTemperature": "Min Module Temperature",
    "NumberOfCharges": "Number of Charges",
    "NumberOfOverDischarging": "Number of Over Discharging",
    "NumberOfCycle": "Number of Cycle",
    "WarningLightActivationList": "Warning Light Activation List",
    "TypeE": "Type",
    "DeliveryDateE": "Delivery Date",
    "WarrentyExpirationDate": "Warrenty Expiration Date",
    "ServerityE": "Serverity",
    "DateTimeE": "Date&Time",
    "CollisionAnalysis": "Collision Analysis",
    "MachineCollisionAnalysis": "Machine Collision Analysis",
    "OperatorCollisionHistory": "Operator Collision History",
    "CollisionRankingByModel": "Collision Ranking by Model",
    "CollisionRankingByEquipment": "Collision Ranking by Equipment",
    "NumberOfCollision": "Number of Collision",
    "CollisionOccurrenceAnalysis": "Collision Occurrence Analysis",
    "DateO": "Date",
    "FrontRear": "Front/Rear",
    "Side": "Side",
    "Vertical": "Vertical",
    "AddressC": "Address",
    "LocationC": "Location",
    "ConnectionAnalysis": "Connection Analysis",
    "ConnectionLog": "Connection Log",
    "PageConnectionAnalysis": "Page Connection Analysis",
    "Categories": "Categories",
    "HoursH": "hrs",
    "MinuteM": "m",
    "Times": "",
    "IP": "IP",
    "TotalOperationMachines": "Total Operation Machines",
    "TotalWorkingTime": "Total Working Time",
    "TotalDrivingTime": "Total Driving Time",
    "TotalIdleTime": "Total Idle Time",
    "TotalOperatingMachines": "Total Operating Machines",
    "DailyWorkingMachines": "Daily Working Machines",
    "IdlePersonal": "Idle Personnel",
    "EcoIndex": "Eco Index",
    "ARank": "A Rank",
    "BRank": "B Rank",
    "CRank": "C Rank",
    "DRank": "D Rank",
    "NoReport": "No Report",
    "UtilIndex": "Util Index",
    "ErrorAlert": "Error Alert",
    "PendingError": "Pending Error",
    "ResponseError": "Response Error",
    "CompletedError": "Completed Error",
    "MaintenanceAlert": "Maintenance Alert",
    "ReplacementDue": "Replacement Due",
    "ReplacementOverdue": "Replacement Overdue",
    "ReplacementCompleted": "Replacement Completed",
    "ChargingCount": "Charging Count",
    "BatteryUsage": "Battery Usage",
    "EnergyEfficiency": "Energy Efficiency",
    "AverageEnergyEfficiency": "Average Energy Efficiency",
    "FuelCost": "Fuel Cost",
    "AverageFuelEfficiency": "Average Fuel Efficiency",
    "FuelUnitPriceSetting": "Fuel Unit Price Setting",
    "MachineUtilizationRateA": "Machine Utilization Rate",
    "NumberOfWorkers": "Number of Workers",
    "AlarmF": "Alarm",
    "TotalWorkingTimeO": "Total Working Time",
    "LocationE": "Location",
    "OperatingEfficiency": "Operating Efficiency",
    "CollisionsPerMachine": "Collisions per Machine",
    "CollisionsPerOperator": "Collisions per Operator ",
    "NumberOfCollisions": "Number of Collisions",
    "WeatherInformation": "Weather Information",
    "MachineName": "Machine Name",
    "Weather": "Weather",
    "Warning": "Warning",
    "FuelUnitPriceSettings": "Fuel Unit Price Settings",
    "FuelUnit": "Fuel Unit",
    "CurrencyUnit": "Currency Unit",
    "Cost": "Cost",
    "SetTemperatureCriteria": "Set Temperature Criteria",
    "HighTemperature": "High Temperature",
    "HighTemperatureSet": "High Temperature",
    "LowTemperature": "Low Temperature",
    "LowTemperatureSet": "Low Temperature",
    "TemperatureUnit": "Temperature Unit",
    "UnitsN": "",
    "Won": "",
    "ConfirmS": "Confirm",
    "VerticalN": "Vertical",
    "SideN": "Side",
    "FR": "F/R",
    "OtherC": "Other",
    "DriverInfo": "Driver Info.",
    "EquipmentInfo": "Equipment Info.",
    "Event": "Event",
    "Etc": "Etc",
    "Holidays": "Holidays",
    "AddC": "Add",
    "AllDay": "All-Day",
    "AddSchedule": "Add Schedule",
    "EditSchedule": "Edit Schedule",
    "TypeC": "Type",
    "Equipment": "Equipment",
    "TitleC": "Title",
    "ContentC": "Content",
    "RepeatR": "Repeat",
    "Start": "Start",
    "End": "End",
    "TimeS": "Time",
    "TimeE": "Time",
    "N": "N",
    "DAILY": "Daily",
    "WEEKLY": "Weekly",
    "MONTHLY": "Monthly",
    "YEARLY": "Yearly",
    "EndDateCannotBeEarlierThanStartDate": "End date cannot be earlier than start date.",
    "NotificationDetails": "Notification Details",
    "Repeat": "Repeat",
    "VisibilityStatus": "Visibility Status",
    "ViewP": "View",
    "AllP": "All",
    "RestrictedP": "Restricted",
    "SelectCountry": "Select Country ",
    "YearAfter": "Year After ",
    "Public": "Public",
    "AddedNewSchedule": "Added New schedule.",
    "DeletingSchedule": "The schedule has been deleted.",
    "Blue": "Blue",
    "Orange": "Orange",
    "Red": "Red",
    "Green": "Green",
    "Purple": "Purple",
    "Gray": "Gray",
    "CountryMultipleSelect": "Country(Multiple Select)",
    "WorkingHoursAnalysis": "Working Hours Analysis",
    "FuelAnalysis": "Fuel Analysis",
    "Heatmap": "Heatmap",
    "APIManagement": "API Management",
    "AutonomousForklift": "Autonomous Forklift",
    "MessagesManagement": "Messages Management",
    "ManagingPermissionGroupUsers": "Managing Permission Group Users",
    "PartNumberLookup": "Part Number Lookup",
    "DATADownload": "DATA Download",
    "ItsNotYetAvailable": "It's not yet available.",
    "MachineLocation": "Machine Location",
    "ReportE": "Report",
    "Maintenance": "Maintenance",
    "DateRefresh": "Date Refresh",
    "DeliveryDateC": "Delivery Date",
    "ServiceDate": "Service Date",
    "CommunicationStatusE": "Communication Status",
    "Available": "Available",
    "OperatingStart": "Operating Start",
    "OperatingEnd": "Operating End",
    "TotalOperatingHoursE": "Total Operating Hours",
    "DailyWorkStatistics": "Work Statistics",
    "EngineOperationChart": "Engine Operation Chart",
    "DailyWorkStatisticsE": "Daily Work Statistics",
    "Top5OfEquipmentUilizationAmongSameModel": "Top 5% of Equipment Uilization Among Same Model",
    "EngineRunT": "Engine Run",
    "EngineRun": "Engine Run",
    "WorkingE": "Working",
    "TravelingE": "Traveling",
    "IdlingE": "Idling",
    "EnginePowerMode": "Engine Power Mode",
    "LastUpdated": "Last Updated",
    "Battery": "Battery",
    "MotorR": "Motor(R)",
    "MotorL": "Motor(L)",
    "Pump": "Pump",
    "EPS": "EPS",
    "Fuel": "Fuel",
    "HydraulicOil": "Hydraulic Oil",
    "EngineCoolant": "Engine Coolant",
    "Transmission": "Transmission",
    "DEF": "DEF",
    "WorkingDay": "Working Day",
    "TotalEngineRunHour": "Total Engine RunHour",
    "EngineRunHourPerDayAvg": "Engine Run Hour per Day(Avg)",
    "DailyOperatingHours": "Daily Operating Hours",
    "FuelConsumptionE": "Fuel Consumption",
    "BatteryStatistics": "Battery Statistics",
    "EstimatedChargingTimeByMachine": "Estimated Charging Time by Machine",
    "AverageBatteryDepletionRateByHour": "Average Battery Depletion Rate by Hour",
    "BatteryDepletionRateComparedToSameModel": "Battery Depletion Rate Compared to Same Model",
    "DepletionRateperOperatingHour": "Depletion Rate per Operating Hour",
    "DepletionRateComparedtoOtherMachine": "Depletion Rate Compared to ther Machine",
    "SameModelAverage": "SameModel Average",
    "SelectedMachine": "Selected Machine",
    "SameModelAverage2": "SameModel Average",
    "TemperatureDistribution": "Temperature Distribution",
    "ADaysHours": "A days hours",
    "TotalAverageHours": "Total Average Hours",
    "OperatingHoursE": "Operating Hours E",
    "FuelUsedTotal": "Fuel Used Total",
    "FuelRateTotalAvg": "Fuel Rate Total(Avg)",
    "EqPeriodicalWarningText": "* If there is a period during which the operation status of equipment is not received due to reasons such as communication issues, the actual operating hours and fuel used may be greater than the total displayed.",
    "Details": "Details",
    "TemperatureDistribution2": "Temperature Distribution",
    "VersionInformation": "Version Information",
    "MonitoringInformation": "Monitoring Information",
    "SendMessage": "Send Message",
    "SendMessageN": "Send",
    "SentReceivedMessage": "Sent/Received Message",
    "SMSEMailTransmissionHistory": "SMS/E-mail Transmission History",
    "ASPhoneNumber": "A/S Phone Number",
    "ASSubDealer": "A/S Sub Dealer",
    "RMSReceivedDataAnalysis": "RMS Received Data Analysis",
    "ServiceHistoryInformation": "Service History Information",
    "CaseBook": "Case Book",
    "SendA": "Send",
    "AlarmCodeList": "Alarm Code List",
    "AlarmMessageNotice": "Alarm Message Notice",
    "NotificationMethod": "Notification Method",
    "EMailSMS": "E-mail & SMS",
    "AppPush": "App Push",
    "AlarmMessage": "Alarm Message",
    "RecipientC": "Recipient",
    "AddRecipient": "Add Recipient",
    "TheMessageHasBeenSent": "The message has been sent.",
    "Maker": "Maker",
    "DescriptionENG": "Description(ENG)",
    "DescriptionKOR": "Description(KOR)",
    "FaultCode2": "Fault Code",
    "AlarmCodeCollection": "Alarm Code Collection",
    "TSGList": "TSG List",
    "TroubleShootingCasebook": "Trouble Shooting Casebook",
    "File": "File",
    "AlarmHistory": "Alarm History",
    "CurrentFaultAlarm": "Current Fault Alarm",
    "DTC": "DTC",
    "Source": "Source",
    "DTCHistory": "DTC History",
    "CI": "CI",
    "DTCDetails": "DTC Details",
    "ElapsedTimeafterKeyOn": "Elapsed Time after Key On",
    "ElapsedTimeafterStarting": "Elapsed Time after Starting",
    "EngineCoolantTemperature": "Engine Coolant Temperature",
    "HydraulicOilTemperatureK": "Hydraulic Oil Temperature",
    "TransmissionOilTemperatureK": "Transmission Oil Temperature",
    "EngineSpeed": "Engine Speed",
    "BatteryVoltage": "Battery Voltage",
    "AlteratorVoltage": "Alterator Voltage",
    "VersionP": "Version",
    "MaintenanceSchedule": "Maintenance Schedule",
    "MaintenanceHistory": "Maintenance History",
    "MaintenanceIntervalHK": "Maintenance Interval(H)",
    "DependingOnTheEquipmentSomeMaintenanceItemMayNotReflectReplacementHistoryFromTheMachine": "* Depending on the equipment, some maintenance items may not reflect replacement history from the machine.",
    "ItemM": "Item",
    "MaintenanceInterval": "Maintenance Interval",
    "Total2": "Total",
    "ReplaceDate": "Replace Date",
    "HoursonItem": "Hours on Item",
    "RemainingTimetoNextMaintenance": "Remaining Time To Next Maintenance",
    "TotalC": "Total",
    "ItemC": "Item",
    "ChangingReplacementCycle": "Changing Replacement Cycle",
    "Cycle": "Cycle",
    "DoYouWantToRequestMaintenanceInformation": "Do you want to request maintenance information?",
    "DoYouWantToResetTheReplacementDays": "Do you want to reset the replacement days?",
    "InformationHasBeenUpdated": "Information has been updated.",
    "ReplacementDaysHaveBeenReset": "Replacement days have been reset.",
    "MachineTravel": "Machine Travel",
    "Latitude": "Latitude",
    "Longitude": "Longitude",
    "GeoFencing": "Geo-Fencing",
    "StatusE": "Status",
    "SettingTimeE": "Setting Time",
    "BasePositionLatLongAlt": "Base Position (Lat. / Long. / Alt.)",
    "LatestLocationReceivedDate": "Latest Location Received Date",
    "LatestLocationName": "Latest Location (Name)",
    "LocalHour": "Local Hour",
    "TimeToReceiveDailyReport": "Time to Receive Daily Report",
    "Engage": "Engage",
    "Release": "Release",
    "SpeedLimitK": "Speed Limit",
    "StartRestriction": "Start Restriction",
    "RMCUUpdate": "RMCU Update",
    "Geofencing": "Geo-fencing",
    "Update": "Update",
    "LastUpdate2": "Last Update",
    "InformationHasBeenRequested": "Information has been requested.",
    "PasswordHasNeenChanged": "Password has been Changed.",
    "ChangeClusterPassword": "Change Cluster Password",
    "PleaseEnter5DigitNumber": "Please enter 5-digit number",
    "CurrentPassword2": "Current Password",
    "NewPassword2": "New Password",
    "VerifyPassword": "Verify Password",
    "PasswordE": "Password",
    "PasswordIncorrect": "Password Incorrect",
    "GeoFencingSetting": "Geo-fencing Setting",
    "RangeSetting": "Range Setting",
    "GeofencingOFF": "Geo-fencing OFF",
    "Radius": "Radius",
    "Default": "Default ",
    "TotalSum": "Total Sum",
    "AveragePerDay": "Average per Day",
    "WorkingR": "Working",
    "FuelLevel2": "Fuel Level",
    "FuelUsedECM": "Fuel Used(ECM)",
    "FuelUsedTank": "Fuel Used(Tank)",
    "WCAUTIONTheFuelUsedDataReflectedAboveIsTheoreticalAndMayDifferFromTheActualUsedBasedOnTheEnvironmentOrHowTheMachineWasOperated": "* CAUTION :The fuel used data reflected above is theoretical and may differ from the actual used; based on the environment or how the machine was operated.",
    "KeyOn": "Key on",
    "Working": "Working",
    "BatteryLevel": "Battery Level",
    "HydraulicOilMaximum": "Hydraulic Oil Maximum",
    "CoolantMaximum": "Coolant (Maximum)",
    "TransmissionOilMaximum": "Transmission Oil (Maximum)",
    "MaxTemperatureRecordHistory": "Max Temperature Record History",
    "LastYearValueBasedOnSearchDate": "* Last year value based on search date",
    "MaxCoolantTemperature": "Max Coolant Temperature",
    "MaxTransmissionOilTemperature": "MaxTransmission Oil Temperature",
    "TransmissionFormat": "Transmission Format",
    "NetworkS": "Network",
    "SMSS": "SMS",
    "SMS": "SMS",
    "TemperatureRequestHasBeenCompleted": "Temperature request has been completed.",
    "MotorRMMaximum": "Motor RM Maximum",
    "MotorLMMaximum": "Motor LM Maximum",
    "MotorPumpMaximum": "Motor Pump Maximum",
    "MotorEPSMaximum": "Motor EPS Maximum",
    "MotorRM": "Motor(RM)",
    "MotorLM": "Motor(LM)",
    "MotorPump": "Motor(Pump)",
    "MotorEPS": "Motor(EPS)",
    "MaxMotorRMTemperature": "Motor(RM) Temperature",
    "MaxMotorLMTemperature": "Motor(LM) Temperature",
    "MaxMotorPumpTemperature": "Motor(Pump) Temperature",
    "MaxMotorEPSTemperature": "Motor(EPS) Temperature",
    "Controller": "Controller",
    "InformationM": "InformationM",
    "ChagenHistoryM": "Chagen History",
    "ManufacturingDate": "Manufacturing Date",
    "ReceptionDate": "Reception Date",
    "ChangeDate": "Change Date",
    "MileageHour": "Hour Meter / Hour",
    "MileageSecond": "Hour Meter / Second",
    "InputBy": "Input By",
    "InputDate": "Input Date",
    "WouldYouLikeToRequestTheRMCUBD": "Title Would you like to request the RMCU BD?",
    "RMCUBDRequestIsComplete": "RMCU BD request is complete.",
    "MCUInformationRequestIsComplete": "MCU information request is complete.",
    "Manufacturer": "Manufacturer",
    "SerialNoK": "Serial No.",
    "PriviousSerialNo": "Privious Serial No.",
    "NewSerialNo": "New Serial No.",
    "Remarks": "Remarks",
    "InformationR": "Information",
    "MCUI": "MCU",
    "HistoryC": "History",
    "Manufacturer2": "Manufacturer",
    "DateS": "Date",
    "ASNumber": "A/S Number",
    "EquipmentMonitorCluster": "Equipment Monitor(Cluster)",
    "ASDealer": "A/S Dealer",
    "PleaseEnterThePhoneNumber": "Please Enter The Phone Number.",
    "ProductionI": "Production",
    "Chassis": "Chassis",
    "DesignM": "Design",
    "ProductionStart": "Production Start",
    "Shipping": "Shipping",
    "CustomerDelivery": "Customer Delivery",
    "EquipmentNo": "Equipment No.",
    "EngineNo": "Engine No.",
    "CustomerN": "Customer",
    "Sales": "Sales",
    "ContactNo": "Contact No.",
    "CustomerName": "Customer",
    "Amount": "Amount",
    "PaymentTerms": "Payment Terms",
    "RequestedDelivery": "Requested Delivery",
    "Optional": "Optional",
    "CodeNo": "Code No.",
    "CodeK": "Code",
    "CodeName": "Code",
    "Spec1Spec2": "Spec 1 / Spec 2",
    "Weight1Weight2": "Weight 1 / Weight 2",
    "PhoneNumber": "Phone Number",
    "Phone": "Phone",
    "Name": "Name",
    "NameE": "Name",
    "MCU": "MCU",
    "RequestM": "Request",
    "RequestR": "Request",
    "InformationBR": "Information",
    "ChangeHistoryR": "Change History",
    "TimeR": "Time",
    "WattingT": "Watting",
    "BatteryUsed": "Battery Used",
    "DateC": "Date",
    "DateR": "Date",
    "MaxHydraulicOilTemperature": "Max Hydraulic Oil Temperature",
    "EngineOperatingHistory": "Engine Operating History",
    "WorkingT": "Working",
    "FuelLevelR": "연료잔량",
    "FuelUsedPerDayAvg": "Fuel Used per Day(Avg)",
    "Setting2": "Setting",
    "ResetKE": "Reset",
    "DateI": "Date",
    "Setting": "Setting",
    "InsideTheDesignatedArea": "Inside The Designated Area.",
    "FaultLocation": "Fault Location",
    "FaultSeverity": "Fault Severity",
    "ConsumableReplacementLevel": "Consumable Replacement Level",
    "Warning2": "Warning",
    "ServiceSoon": "Service Soon",
    "ServiceNow": "Service Now",
    "StopSoon": "Stop Soon",
    "StopNow": "Stop Now",
    "DueDate": "Due Date",
    "OverDue": "Over Due",
    "DealerM": "Dealer",
    "ShipmentDate": "Shipment Date",
    "WarrantyPeriod": "Warranty Period",
    "AlarmI": "Alarm",
    "DateTimeF": "DateTime",
    "DescriptionF": "Description",
    "Submit": "Submit",
    "SetWatchlistEquipment": "SetWatchlist Equipment",
    "DoYouWantToSetThisEquipmentAsWatchlistEquipmentTheSelectedEquipmentCanBeViewedInTheStatistics": "Do you want to set this equipment as watchlist equipment? The selected equipment can be viewed in the statistics.",
    "AreYouSureYouWantToDeleteTheSelectedRegionDeletedInformationCannotBeRecovered": "Are you sure you want to delete the selected region? Deleted information cannot be recovered.",
    "AreYouSureYouWantToRemoveTheSelectedEquipmentFromTheRegion": "Are you sure you want to remove the selected equipment from the region?",
    "AreYouSureYouWantToRemoveTheSelectedDriversFromTheCountry": "Are you sure you want to remove the selected drivers from the country?",
    "TheEquipmentHasBeenSetAsWatchlistEquipment": "The equipment has been set as watchlist equipment.",
    "TheRegionHasBeenDeleted": "The region has been deleted.",
    "TheEquipmentHasBeenRemoved": "The equipment has been removed.",
    "AddRegion": "Add Region",
    "RegionName": "Region Name",
    "RegionNameE": "Region Name",
    "RegionCode": "Region Code",
    "RegionCodeE": "Region Code",
    "ANewRegionHasBeenAdded": "A new region has been added.",
    "AlarmC": "Alarm Content",
    "SeverityF": "Fault Severity",
    "ANewMachineHasBeenRegistered": "A new machine has been registered.",
    "NoSeachResultsFound": "No Seach Results Found.",
    "ShipmentDateE": "Shipment Date",
    "FaultInfo": "Fault Info.",
    "DateTime2": "Date & Time",
    "Symptoms": "Symptoms",
    "SymptomsT": "Symptoms",
    "ResponseMethod": "Response Method",
    "ResponseMethodT": "Response Method",
    "ResponseCompletionStatus": "Response Completion Status",
    "Yes": "Yes",
    "PleaseSetthecountry": "Please Setthe country.",
    "SubmissionHistory": "Submission History",
    "Affiliation": "Affiliation",
    "AffiliationE": "Affiliation",
    "ModifiedDateTime": "Modified Date & Time",
    "Edit2": "Edit",
    "AddContact": "Add Contact",
    "ContactInformation": "Contact Information",
    "ANewContactHasBeenEegistered": "A New contact has been eegistered.",
    "SubmissionHistoryM": "Occurence Date & Time",
    "OccurenceDateTime": "Occurence Date & Time",
    "SubmissionDateTime": "Submission Date & Time",
    "Method": "Method",
    "GuideM": "Guide",
    "StatusM": "Status",
    "Submitter": "Submitter",
    "RegisterSubmission": "Register Submission",
    "Reset": "Reset",
    "FaultK": "Fault",
    "Maint": "Maintenance",
    "Map": "Map",
    "DetailsV": "Details",
    "MechcanicalSafetyDevicesHarnessSeatBeltDataAndWarningLabelsLegibleSafe": "Mechcanical Safety Devices- Harness,Seat Belt, Data and Warning Labels- Legible & Safe",
    "Recently": "Recently",
    "MachineStatus": "Machine Status",
    "Idle": "Idle",
    "InOperation": "In Operation",
    "MaintenanceItem": "Maintenance Item",
    "NoGPSSignal": "No GPS Signal",
    "ViewFilters": "View Filters",
    "HideFilters": "Hide Filters",
    "Chatbot": "Chatbot",
    "PartsRecommendation": "Parts Recommendation",
    "MalfunctionResponseGuide": "Malfunction Response Guide",
    "Notification": "Notification",
    "GoHome": "Home",
    "Korean": "Korean",
    "IDE": "ID",
    "RememberMe": "Remember Me",
    "Join": "Join",
    "FindID": "Find ID",
    "ResetPassword": "Reset Password",
    "UserAgreementPrivacyPolicy": "User Agreement & Privacy Policy",
    "Next": "Next",
    "Logout": "Logout",
    "English": "English",
    "FindByEMailAddress": "Find by E-Mail Address",
    "FindByPhoneNumber": "Find by E-Mail Address",
    "EMailE": "Enter E-Mail",
    "SelectDomain": "Select Domain",
    "EnterPhoneNumberNoDashes": "Enter Phone Number (no dashes)",
    "EMailN": "E-Mail",
    "SendVerificationCode": "Send Verification Code",
    "EnterVerificationCode": "Enter Verification Code",
    "Verify": "Verify",
    "YourIDHasBeenFound": "Your ID has been found.",
    "EnterBetween8To12Characters": "* Enter between 8 to 12 characters.",
    "SelectOutputFileType": "Select output file type",
    "ExcelFileFormatIfYouHaveMSExcel": "Excel file format(If you have MS Excel)",
    "ExcelFileFormatIfYouDontHaveMSExcel": "Excel file format(If you don't have MS Excel)",
    "OKS": "OK",
    "DetailE": "Detail",
    "day": "st",
    "FromYesterday": "from yesterday",
    "Up": "",
    "Down": "",
    "SelectedMachineF": "Selected Machine",
    "RegionA": "Region",
    "Elication": "Elication",
    "PasswordMustBeAtLeast6CharactersLong": "Password must be at least 6 characters long.",
    "FaultE": "Fault",
    "RefFrontRear8GRefSide8GRefVertical8G": "Ref. Front/Rear: 8G, Ref. Side: 8G, Ref. Vertical: 8G",
    "DealerNameD": "Dealer Name",
    "LoactionE": "Loaction",
    "Replace": "Replace",
    "TheFleetHasBeenDeleted": "The fleet has been deleted.",
    "FailedToDeleteFleet": "Failed to delete fleet.",
    "ANewFleetHasBeenAdded": "A new fleet has been added.",
    "AreYouSureYouWantToDeleteTheFleetDeletedDataCannotBeRecovered": "Are you sure you want to delete the fleet?\nDeleted data cannot be recovered.",
    "NoFleetAvailableToAssignWouldYouLikeToAddAFleet": "No fleet available to assign.\nWould you like to add a fleet?",
    "TheSelectedFleetAlreadyHasAssignedEquipmentDoYouWantToContinue": "The selected fleet already has assigned equipment.\nDo you want to continue?",
    "TheDriverHasBeenRemoved": "The driver has been removed.",
    "AreYouSureYouWantToRemoveTheSelectedDriverFromTheEquipment": "Are you sure you want to remove the\nselected driver from the equipment?",
    "TheSelectedInformationHasBeenDeleted": "The selected information has been deleted.",
    "TheSelectedInformationHasBeenDeleted2": "The selected information has been deleted.",
    "AreYouSureYouWantToDeleteTheSelectedDriversOrEquipmentDeletedInformationCannotBeRecovered": "Are you sure you want to delete the selected driver(s) or equipment?\nDeleted information cannot be recovered.",
    "TheDataHasBeenTransmitted": "The data has been transmitted.",
    "TheEquipmentHasBeenDeleted": "The equipment has been deleted.",
    "AreYouSureYouWantToDeleteTheSelectedPreCheck": "Are you sure you want to delete the\nselected Pre-check?",
    "ThePreCheckHasBeenDeleted": "The Pre-check has been deleted.",
    "AreYouSureYouWantToDeleteTheDeviceTheServiceHistroy": "Are you sure you want to delete the device the service histroy?",
    "TheServiceHistoryHasBeenDeleted": "The Service history has been deleted.",
    "AreYouSureYouWantToDeleteTheNotice": "Are you sure you want to delete the notice?",
    "NoticeDeleted": "Notice deleted.",
    "AreYouSureYouWantToDeleteOnceDeletedItCannotBeRecovered": "Are you sure you want to delete?\nOnce deleted, it cannot be recovered.",
    "DoYouWantToLeaveThisPage": "Do you want to leave this page?",
    "ThereAreUnsavedChangesIfYouLeaveWithoutSavingAnyUnsavedContentWillBeLost": "There are unsaved changes. If you leave without saving, any unsaved content will be lost.",
    "AreYouSureYouWantToDeleteTheCrashDetectionSettingsFromTheSelectedEquipmentDeletedInformationCannotBeRecovered": "Are you sure you want to delete the crash detection settings from the selected equipment?\nDeleted information cannot be recovered.",
    "HowToUse": "How to Use",
    "Others": "Others",
    "Campaign": "Campaign",
    "RFID": "RFID",
    "Dealer": "Dealer",
    "SubUser": "Sub User",
    "FOC": "FOC",
    "ServicePeriod": "Service Period",
    "Data": "Data",
    "HW": "H/W",
    "SW": "S/W",
    "SuperAdministrator": "Super Administrator",
    "DelearAdministrator": "Delear Administrator",
    "RepairAdministrator": "Repair Administrator",
    "MMXAdministrator": "MMX Administrator",
    "Actvice": "Actvice",
    "InActive": "InActive",
    "ECD": "ECD",
    "MCD": "MCD",
    "MMDDYY": "MMDDYY",
    "YYMMDD": "YYMMDD",
    "Present": "Present",
    "1DaysAgo": "1days ago",
    "2DaysAgo": "2days ago",
    "3DaysAgo": "3days ago",
    "4DaysAgo": "4days ago",
    "5DaysAgo": "5days ago",
    "DateRefreshT": "Request",
    "DateRefreshC": "Setting",
    "Receipt": "Receipt",
    "MMCStatistics": "MMC Statistics",
    "FailureAlarmOccurrenceRateByPeriod": "Failure Alarm Occurrence Rate by Period",
    "ConsumablesAlarmOccurrenceRateByPeriod": "Consumables Alarm Occurrence Rate by Period",
    "TotalFaultCodes": "Total Fault Codes",
    "GazeEquipmentList": "Gaze Equipment List",
    "ResponsePerformance": "Response Performance",
    "Manager": "Manager",
    "WarrantyPeriodD": "Warranty Period",
    "MySetting": "My Setting",
    "MyMachine": "My Machine",
    "AlarmIng": "Alarm",
    "LatestEquipmentLocation": "Latest Equipment Location",
    "CurrentEquipmentLocation": "Current Equipment Location",
    "ConfirmSetting": "Confirm",
    "Change": "Change",
    "PostingTime": "",
    "MPG": "MPG",
    "OperatingHoursET": "Operating Hours",
    "TypeS": "Type",
    "ItemM2": "Item",
    "UpadateCompleted": "Upadate Completed",
    "ServicePhoneNumber": "Service Phone Number",
    "DealerAS": "Dealer",
    "SubDealerAS": "Sub-Dealer",
    "FleetInfomation": "Fleet Infomation",
    "UnitsV": "Units",
    "AddD": "Add",
    "TypeN": "Type",
    "SystemCheck": "System Check",
    "TermsChanges": "Terms Changes",
    "BatchAssignment": "Batch Assignment",
    "Load": "Load",
    "Today": "Today",
    "Week": "Week",
    "MonthT": "Month",
    "HDHYUNDAIXITESOLUTION": "HD HYUNDAI XITESOLUTION",
    "Terms": "Terms",
    "Conditions": "Conditions",
    "BatteryOperatingHour": "Battery Operating Hour",
    "TheFleetHasBeenAssigned": "The fleet has been assigned.",
    "ANewDriverHasBeenRegistered": "A new driver has been registered.",
    "TheModifiedInformationHasBeenSaved": "The modified information has been saved.",
    "ModelSerialNoMachineID3": "Model / SerialNo / Machine ID",
    "LastRecievedDate": "Last Recieved Date",
    "Reply": "Reply",
    "SouthKorea": "South Korea",
    "82SouthKorea": "+82(South Korea)",
    "Favorites": "Favorites",
    "RemoveFavorite": "Remove Favorite",
    "ViewDetails": "View Details",
    "EXCHANGE": "Exchange",
    "IMMINENT": "Imminent",
    "EXCESS": "Excess",
    "CasesA": "Cases",
    "Response": "Response",
    "EditContact": "Edit Contact",
    "AddS": "Add",
    "AddToFavoritesT": "Add to favorites.",
    "PleaseSetTheCountry": "Please set the country",
    "AddToFavorites": "Add to Favorites",
    "FavoriteName": "Favorite Name",
    "EnterFavoriteName": "Enter Favorite Name",
    "NewFavoriteAdded": "New Favorite Added.",
    "SelectMachine": "Select Machine",
    "DealerDS": "Dealer",
    "AreYouSureYouWantToDeleteTheSelectedContactFromTheCountry": "Are you sure you want to delete the selected contact from the country?",
    "AccumulatedTime": "Accumulated Time",
    "AreYouSureYouWantToRemoveThisFavoriteTheMachineInformationWillNotBeDeleted": "Are you sure you want to remove this favorite? The machine information will not be deleted.",
    "TheFavoriteHasBeenRemoved": "The favorite has been removed.",
    "AreYouSureYouWantToDeleteTheSelectedEquipmentFromYourFavorites": "Are you sure you want to delete the selected equipment from your favorites?",
    "TheMachineHasBeenRemoved": "The machine has been removed.",
    "TheContactHasBeenDeleted": "The contact has been deleted.",
    "WorkingInformation": "Working Information",
    "EngineRunHourTotal": "Engine Run hour(Total)",
    "TimeO": "Time",
    "Day": "Day",
    "FrontRearC": "Front / Rear",
    "SideC": "Side",
    "TopBottomC": "Top / Bottom",
    "SpeedC": "Speed",
    "AccidentC": "Accident",
    "Reset2": "Reset",
    "RegionS": "By Region",
    "CountryS": "By Country",
    "ModelS": "By Model",
    "AreYouSureYouWantToDeleteTheSelectedEquipmentFromTheFleet": "Are you sure you want to delete the selected equipment from the Fleet?",
    "TheCrashDetectionSettingsHaveBeenDeleted": "The crash detection settings have been deleted.",
    "TheCollisionDetectionSettingHasBeenDeleted": "The collision detection setting has been deleted.",
    "TheFleetNameIsAlreadyInUse": "The fleet name is already in use.",
    "NoticeBadge": "notice",
    "CustomerRentalUsedTotal": "customer/rental/used/total",
    "TotalEquipmentOperations": "Total Equipment Operations",
    "PleaseEnterValidEmailAddress": "Please enter a valid email address.",
    "AreYouSureYouWantToDeleteTheQA": "Are you sure you want to delete the Q&A?",
    "QADeleted": "Q&A Deleted",
    "RefFrontRear": "RefFrontRear",
    "RefSide": "RefSide",
    "RefVertical": "RefVertical",
    "Sources": "Sources",
    "InService": "In Service",
    "OutOfService": "Out of Service",
    "More": "More",
    "HoursS": "h",
    "MaintenanceReachedItemTrends": "Maintenance Reached Item Trends",
    "FleetAll": "Fleet All",
    "NoticeEdit": "Notice Edit",
    "ThereIsNoPreviousArticle": "There is no previous article.",
    "ThereIsNoNextArticle": "There is no next article.",
    "DepletionRateComparedToOthers": "Depletion Rate Compared to Others",
    "InProgress": "In Progress",
    "Resolved": "Resolved",
    "Expired": "Expired",
    "Reached": "Reached",
    "MaintenanceExpiredItemTrends": "Maintenance Expired Item Trends",
    "NonOperationS": "Non-Operation",
    "OperationTrend": "Operation Trend",
    "OperationHour": "Operation Hour",
    "AccessLog": "Access Log",
    "GalH": "gal/h",
    "Vehicle": "Vehicle",
    "Truck": "Truck",
    "HeavyEquip": "Heavy Equip.",
    "Agricultural": "aaAgriculturala",
    "Drone": "Drone",
    "ForgotUsername": "Forgot username",
    "PrivacyPolicy": "Privacy Policy",
    "TermsOfUse": "Terms of Use",
    "DontHaveAnAccount": "Don't have an account?",
    "OPTIMIZINGTRANSPORTATIONFLEETMANAGEMENTFORSMARTEROPERATIONS": "OPTIMIZING TRANSPORTATION & FLEET MANAGEMENT FOR SMARTER OPERATIONS",
    "ProfileManagement": "Profile Management",
    "PersonalSetting": "Personal Setting",
    "PersonalInformation": "Personal Information",
    "AccountInformation": "Account Information",
    "UserPermission": "User Permission",
    "TimeSettings": "Time Settings",
    "TimeZoneUTC": "Time Zone (UTC)",
    "FormatUnitSettings": "Format & Unit Settings",
    "DateFormat": "Date Format",
    "DistanceUnit": "Distance Unit",
    "VolumeUnit": "Volume Unit",
    "PressureUnit": "Pressure Unit",
    "WeightUnit": "Weight Unit",
    "Done": "Done",
    "VsPerviousDay": "vs Pervious day",
    "TotalOperatingVehicle": "Total Operating Vehicle",
    "TotalVehicleOperations": "Total Vehicle Operations",
    "MaintenanceReminder": "Maintenance Reminder",
    "DueSoon": "Due Soon",
    "Replaced": "Replaced",
    "RefuelingCount": "Refueling Count",
    "AvgVehicleUtilization": "Avg. Vehicle Utilization",
    "DrivingEfficiency": "Driving Efficiency",
    "VehicleImpactCount": "Vehicle Impact Count",
    "DriverImpactCount": "Driver Impact Count",
    "FrontRearImpact": "Front/Rear Impact",
    "SideImpact": "Side Impact",
    "VerticalImpact": "Vertical Impact",
    "EquipmentName": "Equipment Name",
    "TemperatureThresholdSetting": "Temperature Threshold Setting",
    "Alert": "Alert",
    "TemperatureThresholdSettings": "Temperature Threshold Settings",
    "H": "h",
    "VehicleNumber": "Vehicle Number",
    "AllFleets": "All Fleets",
    "EquipmentNumber": "Equipment Number",
    "AssetID": "Asset ID",
    "Filter": "Filter",
    "EquipmentStatus": "Equipment Status",
    "Received": "Received",
    "EquipmentStatusMap": "Equipment Status Map",
    "TrackingMap": "Tracking Map",
    "InMaintenance": "In Maintenance",
    "ReceiveNotificationsOnTheWeb": "Receive notifications on the web",
    "ReceiveNotificationsInTheApp": "Receive notifications in the app",
    "MaintenanceAlerts": "Maintenance Alerts",
    "ImpactAlerts": "Impact Alerts",
    "OtherAlerts": "Other Alerts",
    "EquipmentDetails": "Equipment Details",
    "FaultInformation": "Fault Information",
    "FaultDateTime": "Fault Date & Time",
    "AlertType": "Alert Type",
    "SeverityLevel": "Severity Level",
    "FaultSymptoms": "Fault Symptoms",
    "FaultPhoto": "Fault Photo",
    "AlertSent": "Alert Sent",
    "MaintenanceInformation": "Maintenance Information",
    "PartName": "Part Name",
    "ReplacementInterval": "Replacement Interval",
    "UsageTime": "Usage Time",
    "LastReplacementDate": "Last Replacement Date",
    "ReplacementDetail": "Replacement Detail",
    "DealershipInformation": "Dealership Information",
    "ServiceCenterInformation": "Service Center Information",
    "ServiceCenter": "Service Center",
    "Address": "Address",
    "SendAlert": "Send Alert",
    "DispatchInfortmation": "Dispatch Infortmation",
    "TotalTasks": "Total Tasks",
    "DeliveryAddress": "Delivery Address",
    "CompletionTime": "Completion Time",
    "VehicleInfo": "Vehicle Info",
    "Awaiting": "Awaiting",
    "Delievered": "Delievered",
    "Failed": "Failed",
    "InTransit": "In Transit",
    "TaskType": "Task Type",
    "BranchName": "Branch Name",
    "FullAddress": "Full Address",
    "DriverName": "Driver Name",
    "ArrivalTime": "Arrival Time",
    "Timeline": "Timeline",
    "VehicleInformation": "Vehicle Information",
    "DeliveryInformation": "Delivery Information",
    "MoterR": "Moter(R)",
    "MoterL": "Moter(L)",
    "TransmissionFluid": "Transmission Fluid",
    "NoFaultHistory": "No Fault History",
    "NoConsumablesToInspectOrReplace": "No consumables to inspect or replace.",
    "TodaysDistance": "Today's Distance",
    "TotalDistance": "Total Distance",
    "NewCases": "New Cases",
    "FaultAlert": "Fault Alert",
    "ConsumableAlert": "Consumable Alert",
    "DrivingPattern": "Driving Pattern",
    "OverspeedCount": "Overspeed Count",
    "AverageSpeed": "Average Speed",
    "HarshBrakingCount": "Harsh Braking Count",
    "HarshAccelerationCount": "Harsh Acceleration Count",
    "DrivingTime": "Driving Time",
    "IdleTime": "Idle Time",
    "DrivingDistance": "Driving Distance",
    "30DayAverage": "30 Day \n Average",
    "VehicleLocation": "Vehicle Location",
    "ViewSpecifications": "View Specifications",
    "LocalDateTime": "Local Date & Time",
    "OperationStart": "Operation Start",
    "OperationEnded": "Operation Ended",
    "TotalOperationTime": "Total Operation Time",
    "Category": "Category",
    "OperationTime": "Operation Time",
    "OperationDate": "Operation Date",
    "AvgDailyTime": "Avg. Daily Time",
    "TotalFuelConsumption": "Total Fuel Consumption",
    "AverageDailyFuelConsumption": "Average Daily Fuel Consumption",
    "Lh": "L/h",
    "DrivingTimeN": "Driving\nTime",
    "IdleTimeN": "Idle\nTime",
    "OperationTimeRatio": "Operation Time Ratio",
    "Ratio": "Ratio",
    "IdlingTime": "Idling Time",
    "CustomRange": "Custom Range",
    "FaultHistory": "Fault History",
    "AllFaultSeverity": "All Fault Severity",
    "AllFaultType": "All Fault Type",
    "AllStatus": "All Status",
    "High": "High",
    "Medium": "Medium",
    "Low": "Low",
    "FaultOccurenceTime": "Fault Occurence Time",
    "FaultType": "Fault Type",
    "FaultImage": "Fault Image",
    "MaintenanceDetails": "Maintenance Details",
    "AlarmTypeN": "Alarm\nType",
    "FaultTypeN": "Fault\nType",
    "FaultSeverityN": "Fault\nSeverity",
    "FaultImageN": "Fault\nImage",
    "SendMessageNN": "Send\nMessage",
    "MaintenanceDetailsN": "Maintenance\nDetails",
    "Faults": "Faults",
    "Consumables": "Consumables",
    "MoreInfo": "More Info",
    "WorkHistory": "Work History",
    "Settings": "Settings",
    "CurrentFaultAlerts": "Current Fault Alerts",
    "ConsumablesStatus": "Consumables Status",
    "ConsumablesHistory": "Consumables History",
    "CurrentFaultInformation": "Current Fault Information",
    "ConsumableItem": "Consumable Item",
    "ReplacementCycleDays": "Replacement Cycle(Days)",
    "LastMaintenanceMileageReplacementDate": "Last Maintenance\n(Mileage/Replacement Date)",
    "RemainingDays": "Remaining Days",
    "ConsumableReplacement": "Consumable Replacement",
    "IntervalUpdate": "Interval Update",
    "TaskDate": "Task Date",
    "ConfiguredValue": "Configured Value",
    "VehicleSpecifications": "Vehicle Specifications",
    "DrivingResistanceFactors": "Driving Resistance Factors",
    "AerodynamicDragCoefficient": "Aerodynamic Drag Coefficient",
    "TirePressure": "Tire Pressure",
    "TruckConfiguration": "Truck Configuration",
    "TireDiameter": "Tire Diameter",
    "TireWidth": "Tire Width",
    "HazardousMaterialsType": "Hazardous Materials Type",
    "NumberOfAxles": "Number of Axles",
    "NumberOfWheels": "Number of Wheels",
    "NumberOfTrailers": "Number of Trailers",
    "KingpinToRearAxleFtM": "Kingpin to Rear Axle(ft/m)",
    "LiftingEquipmentInstalled": "Lifting Equipment Installed",
    "RefrigerationUnitInstalled": "Refrigeration Unit Installed",
    "DeleteFleet": "Delete Fleet",
    "AreYouSureYouWantToDeleteThisFleetDeletedInformationCannotBeRecovered": "Are you sure you want to delete this fleet?\nDeleted information cannot be recovered.",
    "EquipmentList": "Equipment List",
    "EquipmentControl": "Equipment Control",
    "ControlHistory": "Control History",
    "ModelName": "Model Name ",
    "VehicleNumber ": "Vehicle Number ",
    "DealershipName": "Dealership Name",
    "EquipmentType": "Equipment Type",
    "AreYouSureYouWantToRemoveTheSelectedEquipmentFromTheFleet": "Are you sure you want to remove the selected equipment\nfrom the fleet?",
    "RemoveEquipment": "Remove Equipment",
    "AddSuccess": "Successfully added.",
    "AddFail": "Failed to add.",
    "DeleteSuccess": "Successfully deleted.",
    "DeleteFail": "Failed to delete.",
    "ModifySuccess": "Successfully modified.",
    "ModifyFail": "Failed to modify.",
    "EquipmentInformationN": "Equipment\nInformation",
    "AvailableEquipmentCount": "Available Equipment Count",
    "RegisterEquipment": "Register Equipment",
    "TheNewEquipmentHasBeenRegistered": "The new equipment has been registered.",
    "ImpactManagement": "Impact Management",
    "FleetImpactList": "List",
    "FleetImpactReport": "Report",
    "AllWorkStatus": "All Work Status",
    "NotStarted": "Not Started",
    "DeleteDriver": "Delete Driver",
    "AreYouSureYouWantToDeleteTheSelectedDriverDeletedInformationCannotBeRecovered": "Are you sure you want to delete the selected driver?\nDeleted information cannot be recovered.",
    "TheSelectedDriverHasBeenDeleted": "The selected driver has been deleted.",
    "Import": "Import",
    "DriverDetails": "Driver Details",
    "LicenseExpirationDate": "License Expiration Date",
    "AssignedEquipment": "Assigned Equipment",
    "WorkLog": "Work Log",
    "Trim": "Trim",
    "YearOfManufacture": "Year of Manufacture",
    "VINNumber": "VIN Number",
    "AssignableEquipment": "Assignable Equipment",
    "AreYouSureYouWantToDeleteTheSelectedEquipmentAssignedToThisDriver": "Are you sure you want to delete the selected equipment\nassigned to this driver?",
    "TheSelectedEquipmentHasBeenRemoved": "The selected equipment has been removed.",
    "DriverIDNumber": "Driver ID Number",
    "IssuingState": "Issuing State",
    "LicenseNumber": "License Number",
    "Class": "Class",
    "DriverInformationHasBeenUpdated": "Driver information has been updated.",
    "Drivers": "Drivers",
    "TotalWorkDuration": "Total Work Duration",
    "DistanceTraveled": "Distance Traveled",
    "ImpactInformationReport": "Impact Information Report",
    "OverspeedThreshold": "Overspeed Threshold",
    "Unsent": "Unsent",
    "ImpactThresholdG": "Impact Threshold (G)",
    "AccidentCriteriaG": "Accident Criteria (G)",
    "FrontRearSideVertical": "(Front/Rear, Side, Vertical)",
    "DeleteImpactInformation": "Delete Impact Information",
    "AreYouSureYouWantToDeleteTheSelectedImpactInformationDeletedDataCannotBeRecovered": "Are you sure you want to delete the selected impact information?Deleted data cannot be recovered.",
    "TheSelectedImpactInformationHasBeenDeleted": "The selected impact information has been deleted.",
    "UnitNo": "Unit No.",
    "ImpactThreshold": "Impact Threshold",
    "ValidRange01250": "Valid range: 0.1 ~ 25.0",
    "FontRear": "Font/Rear",
    "ValidRange01100": "Valid range: 0.1 ~10.0",
    "ValidRange130": "Valid range: 1 ~ 30",
    "ImpactTime": "Impact Time",
    "ImpactInformation": "Impact Information",
    "ThresholdSetting": "Threshold Setting",
    "EquipmentInformation": "Equipment Information",
    "AccidentCriteria": "Accident Criteria",
    "VehicleManagement": "Vehicle Management",
    "AllManufacture": "All Manufacture",
    "VehicleNo": "Vehicle No.",
    "NewestFirst": "Newest First",
    "LeaveThisPage": "Leave This Page?",
    "YouHaveUnsavedChangesIfYouLeaveThisPageNowAllUnsavedDataWillBeLost": "You have unsaved changes.\nIf you leave this page now, all unsaved data will be lost.",
    "Leave": "Leave",
    "LoadDraft": "Load Draft",
    "SaveAsDraft": "Save as Draft",
    "BasicInformation": "Basic Information",
    "ConsumableItemsAndReplacementMaintenanceCriteria": "Consumable Items and Replacement/Maintenance Criteria",
    "Efficiency": "Efficiency",
    "FleetAssignment": "Fleet Assignment",
    "TrimName": "Trim Name",
    "ContactNumber": "Contact Number",
    "StreetAddress": "Street Address",
    "AptSuitUnit": "Apt/Suit/Unit",
    "ZIPCode": "ZIP Code",
    "TruckType": "Truck Type",
    "Length": "Length",
    "Height": "Height",
    "Width": "Width",
    "HazardousMaterials": "Hazardous Materials",
    "Explosives": "Explosives",
    "Gas": "Gas",
    "Flammable": "Flammable",
    "Organic": "Organic",
    "Poison": "Poison",
    "Radioactive": "Radioactive",
    "Corrosive": "Corrosive",
    "HarmfulForWater": "Harmful For Water",
    "PosionousInhaltionHazard": "Posionous Inhaltion Hazard",
    "Other": "Other",
    "Mi": "mi",
    "km": "km",
    "EngineOil": "Engine Oil",
    "OilFilter": "Oil Filter",
    "FuelFilter": "Fuel Filter",
    "AirFilter": "Air Filter",
    "BrakePads": "Brake Pads",
    "BrakeLining": "Brake Lining",
    "Tires": "Tires",
    "Coolant": "Coolant",
    "TransmissionOil": "Transmission Oil",
    "FuelType": "Fuel Type",
    "SearchForAnAddress": "Search for an address",
    "FuelTankCapacity": "Fuel Tank Capacity",
    "FuelEfficiencyKmLi": "Fuel Efficiency(km/Li)",
    "FuelTankCapacityL": "Fuel Tank Capacity(L)",
    "SelectFleet": "Select Fleet",
    "Selected": "Selected",
    "BusinessName": "Business Name",
    "Inspect": "Inspect",
    "SafeDrivingScore": "Safe Driving Score",
    "ConsumablesManagement": "Consumables Management",
    "WorkLogSummary": "Work Log Summary",
    "TotalVehicles": "Total Vehicles",
    "Itinerary": "Itinerary",
    "OnDuty": "On Duty",
    "OffDuty": "Off Duty",
    "UserManagement": "User Management",
    "DealershipEquipmentStatus": "Dealership Equipment Status",
    "AllCountries": "All Countries",
    "AllDelarships": "All Delarships",
    "FaultCodeList": "Fault Code List",
    "ThereIsAVehicleInvolvedInAnAccident": "There is a vehicle involved in an accident.",
    "DeliveryStatus": "Delivery Status",
    "Incomplete": "Incomplete",
    "TotalEquipment": "Total Equipment",
    "Waypoint": "Waypoint",
    "AddWaypoint": "Add Waypoint",
    "TitleOrContent": "Title or Content",
    "AllNotice": "All Notice",
    "SystemMaintenance": "System Maintenance",
    "TermsUpdate": "Terms Update",
    "GeneralUpdate": "General Update",
    "NoticeType": "Notice Type",
    "Author": "Author",
    "Views": "Views",
    "PostedDate": "Posted Date",
    "AreYouSureYouWantToDeleteThisNotice": "Are you sure you want to delete this notice?",
    "TheNoticeHasBeenDeleted": "The notice has been deleted.",
    "DisplayPeriod": "Display Period",
    "AlwaysVisible": "Always Visible",
    "ScheduledPost": "Scheduled Post",
    "ViewPermission": "View Permission",
    "PinnedToTop": "Pinned to Top",
    "AreYouSureYouWantToDeleteThisYemporarySaveThisActionCannotBeUndone": "Are you sure you want to delete this temporary save?\nThis action cannot be undone.",
    "TheTemporarySaveHasBeenDeleted": "The temporary save has been deleted.",
    "AllInquiryType": "All Inquiry Type",
    "InquiryType": "Inquiry Type",
    "Pinned": "Pinned",
    "DeleteNotice": "Delete Notice",
    "DeleteFAQ": "Delete FAQ",
    "AreYouSureYouWantToDeleteThisFAQ": "Are you sure you want to delete this FAQ?",
    "TheFAQHasBeenDeleted": "The FAQ has been deleted.",
    "401error": "401error",
    "YouAreNotAuthorizedToAccessThisPagePleaseLogInAndTryAgain": "You are not authorized to access this page.\nPlease log in and try again.",
    "IdleVehicles": "Idle Vehicles",
    "UnderMaintenance": "Under Maintenance",
    "Descending": "Descending",
    "Ascending": "Ascending",
    "NoReportedIssuesForThisVehicle": "No reported issues for this vehicle.",
    "NoConsumablesRequireInspectionOrReplacement": "No consumables require inspection or replacement.",
    "NoDealerInformationAvailable": "No dealer information available.",
    "NoServiceCenterInformationAvailable": "No service center information available.",
    "DriveType": "Drive Type",
    "SetInterval": "Set Interval",
    "ManufacturerName": "Manufacturer Name",
    "DispatchHistory": "Dispatch History",
    "ItineraryTitle": "Itinerary Title",
    "ItineraryDate": "Itinerary Date",
    "TotalStops": "Total Stops",
    "CompletedDeliveries": "Completed Deliveries",
    "SearchResultsNotFound": "Search results not found.",
    "NoAddressesFound": "No addresses found.",
    "Searching": "Searching",
    "VehicleAssignedToDriver": "Vehicle Assigned to Driver",
    "SelectOtherVehicle": "Select Other Vehicle",
    "DispatchDetails": "Dispatch Details",
    "ItineraryInformation": "Itinerary Information",
    "VehicleList": "Vehicle List",
    "VehicleControl": "Vehicle Control",
    "AddVehicle": "Add Vehicle",
    "VINNo": "VIN No.",
    "ManufactureYear": "Manufacture Year",
    "VehicleType": "Vehicle Type",
    "DriversLicenseNumber": "Driver's License Number",
    "ManufactureName": "Manufacture Name",
    "BodyClass": "Body Class",
    "TireRotation": "Tire Rotation",
    "TireWidthmm": "Tire Width(mm)",
    "TireDiameterinch": "Tire Diameter(inch)",
    "SelectDealer": "Select Dealer",
    "SelectServiceCenter": "Select Service Center",
    "HighTemperatureThreshold": "High Temperature Threshold",
    "LowTemperatureThreshold": "Low Temperature Threshold",
    "DeliveryOrder": "Delivery Order",
    "RemoveVehicle": "Remove Vehicle",
    "TheSelectedVehicleHasBeenUnassignedFromDriver": "The selected vehicle has been unassigned from driver.",
    "PleaseChooseHowYoudLikeToRegisterDrivers": "Please choose how you'd like to register drivers.",
    "UploadViaExcel": "Upload via Excel",
    "RegisterManually": "Register Manually",
    "DragAndDropAfileOrClick": "Drag and Drop a file or click.",
    "DontHaveTheDriverRegistrationFormClickTheButtonBelowToDownloadIt": "Don't have the driver registration form?\nClick the button below to download it.",
    "CancelUpload": "Cancel Upload",
    "UploadingDriverListPleaseWaitAMoment": "Uploading driver list...\nPlease wait a moment.",
    "TheDriverListUploadHasNotBeenCompletedDoYouWantToCancelTheUpload": "The driver list upload has not been completed.\nDo you want to cancel the upload?",
    "UploadCanceled": "Upload Canceled",
    "IncidentManagement": "Incident Management",
    "DealerServiceCenterManagement": "Dealer & Service Center Management",
    "DriverListHasBeenSuccessfullyUploaded": "Driver list has been successfully uploaded.",
    "PhoneNumberNoDashes": "Phone number (no dashes)",
    "812Characters": "8 - 12 characters.",
    "MustIncludeAtLeast3OfTheFollowing": "Must include at least 3 of the following:",
    "UppercaseLettersAZ": "- Uppercase letters (A-Z)",
    "LowercaseLettersaz": "- Lowercase letters (a-z)",
    "Numbers09": "- Numbers (0-9)",
    "SpecialCharacters!@#$%^*()-_=+~": "- Special characters ( !@#$%^*()-_=+~ )",
    "CannotContain3IdenticalOrSequentialCharacters": "Cannot contain 3 identical or sequential characters (e.g. aaa, 123)",
    "LicenseInformation": "License Information",
    "ExpirationDate": "Expiration Date",
    "BasicInformationOptional": "Basic Information(Optional)",
    "Gender": "Gender",
    "VehicleRegistration": "Vehicle Registration",
    "IfYouHaveACompanyAssignedIDEGEmployeeNumberPleaseEnterIt": "If you have a company-assigned ID (e.g., employee number), please enter it.",
    "DriverHasBeenRegistered": "Driver has been registered.",
    "AccidentManagement": "Accident Management",
    "Sedan": "Sedan",
    "Hatchback": "Hatchback",
    "Coupe": "Coupe",
    "Converible": "Converible",
    "SUV": "SUV",
    "VanMinivan": "Van / Minivan",
    "Wagon": "Wagon",
    "PickupTruck": "Pickup Truck",
    "BusMinibus": "Bus / Minibus",
    "HeavyDutyTruck": "Heavy-Duty Truck",
    "OffRoadVehicle": "Off-Road Vehicle",
    "Trailer": "Trailer",
    "ThreeWheelMotocycle": "Three-Wheel Motocycle",
    "IncompleteVehicle": "Incomplete Vehicle",
    "Diesel": "Diesel",
    "Gasoline": "Gasoline",
    "Hybrid": "Hybrid",
    "Electric": "Electric",
    "Ft": "ft",
    "In": "in",
    "MKm": "m/Km",
    "FtMi": "ft/mi",
    "NotSubmitted": "Not Submitted",
    "Submitted": "Submitted",
    "ChangingTheStatusToMaintenanceCompletedWillLeadToTheReportSubmissionStep": "* Changing the status to 'Maintenance Completed' will lead to the report submission step.",
    "ChangeStatus": "Change Status",
    "VehicleSelect": "Vehicle Select",
    "NumberNoDashes": "Number (no dashes)",
    "IfYouHaveAnEmployeeIDOrInternalManagementIDPleaseEnterIt": "If you have an employee ID or internal management ID, please enter it.",
    "Stay": "Stay",
    "YouHaveUnsavedChangesIfYouLeaveNowAllEnteredInformationWillBeLostTheMaintenanceStatusWillNotBeUpdatedToCompletedUnlessTheReportIsSubmitted": "You have unsaved changes. If you leave now, all entered information will be lost. The maintenance status will not be updated to 'Completed' unless the report is submitted.",
    "Validate": "Validate",
    "Ph": "Ph.",
    "SetReplacementInterval": "Set Replacement Interval",
    "TheReplacementIntervalHasBeenUpdated": "The replacement interval has been updated.",
    "AreYouSureYouWantToResetTheReplacementDaysForThisConsumableItem": "Are you sure you want to reset the replacement days for this consumable item?",
    "ResetReplacementDays": "Reset Replacement Days",
    "NoHistory": "No History.",
    "EngineConsumption": "Engine Consumption",
    "DealerServiceCenter": "Dealer/Service Center",
    "AccidentHistory": "Accident History",
    "LithiumBatteryInfo": "Lithium Battery Info",
    "CautionTheMessageBelowWillBeSentTheTheDriverViaAppPushNotification": "* Caution : The message below will be sent the the driver via app push nofitication.",
    "DailyAverage": "Daily Average",
    "WorkTime": "Work Time",
    "EngineOperationTime": "Engine Operation Time",
    "FuelConsumptionECM": "Fuel Consumption(ECM)",
    "FuelConsumptionTank": "Fuel Consumption(Tank)",
    "PleaseFillInTheRequiredFields": "Please fill in the required fields.",
    "AResetLinkWillBeSentToTheDriversEmailEmailRequestsAreLimitedToOncePerMinute": "A reset link will be sent to the driver's email.\nEmail requests are limited to once per minute.",
    "DriverNameIsRequired": "Driver name is required.",
    "DriverNameCannotExceed20Characters": "Driver name cannot exceed 20 characters.",
    "OnlyKoreanAndEnglishLettersAreAllowed": "Only Korean and English letters are allowed.",
    "CountryIsRequired": "Country is required.",
    "PhoneNumberIsRequired": "Phone number is required.",
    "PhoneNumberMustBeNumericAndUpTo15Digits": "Phone number must be numeric and up to 15 digits.",
    "EmailIsRequired": "Email is required.",
    "EmailCannotExceed64Characters": "Email cannot exceed 64 characters.",
    "InvalidEmailAddress": "Invalid email address.",
    "PasswordIsRequired": "Password is required.",
    "PasswordMustBeAtLeast8Characters": "Password must be at least 8 characters.",
    "PasswordMustBeAtMost12Characters": "Password must be at most 12 characters.",
    "PasswordMustIncludeLettersNumbersAndSpecialCharacters": "Password must include letters, numbers, and special characters.",
    "PasswordCannotContainSameCharacterRepeated3Times": "Password cannot contain same character repeated 3 times.",
    "IssuingStateIsRequired": "Issuing state is required.",
    "LicenseNumberIsRequired": "License number is required.",
    "ClassIsRequired": "Class is required.",
    "ExpirationDateIsRequired": "Expiration date is required.",
    "IDNumberCannotExceed20Characters": "ID number cannot exceed 20 characters.",
    "FAQRegistration": "FAQ Registration",
    "FAQModify": "FAQ Modify",
    "NoticeRegistration": "Notice Registration",
    "NoticeModify": "Notice Modify",
    "TheFAQHasBeenPosted": "The FAQ has been posted.",
    "YourDraftHasBeenSaved": "Your draft has been saved.",
    "DealerServiceCenterInformation": "Dealer/Service Center Information",
    "DealerCompanyName": "Dealer Company Name",
    "AccidentTime": "Accident Time",
    "ResponseStatus": "Response Status",
    "TotalChargeCycles": "Total Charge Cycles",
    "BTMSPowerConsumption": "BTMS Power Consumption",
    "MaximumBatteryTemperature": "Maximum Battery Temperature",
    "CoolingFanUsage": "Cooling Fan Usage",
    "BatteryPackCycleCount": "Battery Pack Cycle Count",
    "MaximumCellVoltage": "Maximum Cell Voltage",
    "MinimumCellVoltage": "Minimum Cell Voltage",
    "MaximumModuleTemperature": "Maximum Module Temperature",
    "MinimumModuleTemperature": "Minimum Module Temperature",
    "CoolentTemperature": "Coolent Temperature",
    "BatteryToBTMS": "Battery to BTMS",
    "BTMSToVehicle": "BTMS to Vehicle",
    "VehicleToBTMS": "Vehicle to BTMS",
    "AssignedVehicle": "Assigned Vehicle",
    "TrialEnds": "Trial Ends",
    "404NotFound": "404 Not Found",
    "500MethodNotAllowed": "500 Method Not Allowed",
    "ThePageYoureLookingForDoesntExistOrMayHaveBeenMovedPleaseCheckTheURLOrReturnToTheHomepage": "The page you're looking for doesn't exist or may have been moved.\nPlease check the URL or return to the homepage.",
    "TheMethodYouUsedIsNotAllowedForThisRequestPleaseContactTheAdministratorIfTheIssuePersists": "The method you used is not allowed for this request.\nPlease contact the administrator if the issue persists.",
    "ContactSupport": "Contact Support",
    "CreateAccount": "Create Account",
    "PleaseCreateAnAccountToStartYourTrial": "Please create an account to start your trial.",
    "FirstName": "First Name",
    "LastName": "Last Name",
    "PasswordConfirm": "Password Confirm",
    "AlreadyHaveAnAccount": "Already have an account?",
    "ThisEmailIsAlreadyInUse": "This email is already in use.",
    "PleaseEnterBetween8And12Characters": "Please enter between 8 and 12 characters.",
    "AccountVerification": "Account Verification",
    "AVerificationEmailHasNeenSentToTheAddressYouProvidedPleaseClickTheVerifyEmailButtonBelowToCompleteYourRegistration": "A verification email has been sent to the address you provided (<EMAIL>).\nPlease click the Verify Email button below to complete your registration.",
    "VerifyEmail": "Verify Email",
    "DidntReceiveTheEmail": "Didn't receive the email?",
    "ResendEmail": "Resend Email",
    "COPYRIGHT2025CARTAMobilityConfidentialProprietary": "COPYRIGHT © 2025 CARTA Mobility. Confidential & Proprietary",
    "PleaseEnterYourUsername": "Please enter your username.",
    "ThisAccountHasBeenLockedDueToTooManyFailedLoginAttemptsPleaseResetYourPasswordToRegainAccess": "This account has been locked due to too many failed login attempts.\nPlease reset your password to regain access.",
    "TooManyLoginAttempts": "Too many login attempts",
    "LoginFailed": "Login Failed",
    "TemporaryErrorSystemAccessFailedPleaseTryAgainLater": "Temporary Error\nSystem access failed. Please try again later.",
    "IncorrectUsernameOrPassword": "Incorrect username or password.",
    "PleaseEnterTheEmailAddressYouUsedToSignUp": "Please enter the email address you used to sign up.",
    "EnterEmail": "Enter Email",
    "BackToLogin": "Back to Login",
    "NoMatchFoundPleaseCheckndTryAgain": "No match found. Please check and try again.",
    "WevSentAPasswordResetLinkToTheEmailYouEntered": "We've sent a password reset link to the email you entered\n (<EMAIL>).\nPlease check your inbox and follow the instructionsn\nto reset your password.",
    "IfTheButtonDoesntWorkProperlyPleaseCopyAndPasteTheLinkBelowIntoYourBrowser": "If the button doesn't work properly, please copy and paste the link below into your browser:",
    "APasswordResetEmailHasBeenResent": "A password reset email has been resent.",
    "PleaseEnterYourNewPassword": "Please enter your new password.",
    "EnterNewPassword": "Enter new password",
    "PasswordRequirements": "Password Requirements",
    "ForSecurityYouWillNeSignedOutOfAllDevicesAndBrowsersWhenYourPasswordIsChanged": "For security, you will be signed out of all devices and browsers\nwhen your password is changed.",
    "DeleteQA": "Delete Q&A",
    "AreYouSureYouWantToDeleteThisQA": "Are you sure you want to delete this Q&A?",
    "TheQAHasBeenDeleted": "The Q&A has been deleted.",
    "QAType": "Q&A Type",
    "QARegistration": "Q&A Registration",
    "FaultDetails": "Fault Details",
    "VehicleDriverInformation": "Vehicle & Driver Information",
    "MaintenanceCost": "Maintenance Cost",
    "FaultOccurrence": "Fault Occurrence",
    "MaintenanceCompletion": "Maintenance Completion",
    "AtBreakdown": "At Breakdown",
    "AtCompletion": "At Completion",
    "CompanyName": "Company Name",
    "LatitudeLongitude": "Latitude / Longitude",
    "IssueDescription": "Issue Description",
    "FaultPhotos": "Fault Photo(s)",
    "RepairDetails": "Repair Details",
    "CompletionPhotos": "Completion Photo(s)",
    "MaintenanceCompletionReport": "Maintenance Completion Report",
    "MaintenanceReportSubmitted": "Maintenance report submitted.",
    "MaintenanceCode": "Maintenance Code",
    "AllSeverity": "All Severity",
    "FaultOccurrenceTime": "Fault Occurrenc Time",
    "ReplacementCycleH": "Replacement Cycle(h)",
    "NextReplacementDateH": "Next Replacement Date(h)",
    "aaa": "aaa",
    "aaa": "aaa",
    "aaa": "aaa",
    "aaa": "aaa",
    "aaa": "aaa",
    "aaa": "aaa",
    "aaa": "aaa",
    "aaa": "aaa",
    "aaa": "aaa",
    "aaa": "aaa",
    "aaa": "aaa"
  }
}
